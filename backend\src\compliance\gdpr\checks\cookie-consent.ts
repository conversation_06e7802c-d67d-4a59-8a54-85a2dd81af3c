import { GdprCheckResult, Evidence, Recommendation } from '../types';
import {
  EnhancedCheckTemplate,
  EnhancedCheckConfig,
  CheckExecutionContext,
  CheckResult,
} from '../utils/enhanced-check-template';

export interface CookieConsentCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId?: string;
}

interface ConsentChoice {
  type: 'accept' | 'reject';
  text: string;
}

interface InteractionResult {
  consentChoices?: ConsentChoice[];
  cookiesBeforeConsent: number;
  cookiesAfterConsent: number;
  bannerVisible: boolean;
  interactionSuccessful?: boolean;
}

export class CookieConsentCheck {
  private checkTemplate: EnhancedCheckTemplate;

  constructor() {
    this.checkTemplate = new EnhancedCheckTemplate();
  }

  /**
   * Check cookie consent banner compliance
   * ENHANCED IMPLEMENTATION - uses smart content discovery and interaction testing
   */
  async performCheck(config: CookieConsentCheckConfig): Promise<GdprCheckResult> {
    const enhancedConfig: EnhancedCheckConfig = {
      targetUrl: config.targetUrl,
      timeout: config.timeout,
      scanId: config.scanId,
      retryAttempts: 3,
      enableJavaScript: true,
      enableImages: false,
      followRedirects: true,
    };

    return this.checkTemplate.executeCheck(
      'GDPR-004',
      'Cookie Consent Banner',
      'cookies',
      8,
      'high',
      enhancedConfig,
      this.executeCookieConsentCheck.bind(this),
      true, // Requires browser
      false, // No manual review required
    );
  }

  /**
   * Execute the cookie consent check logic
   */
  private async executeCookieConsentCheck(context: CheckExecutionContext): Promise<CheckResult> {
    const startTime = Date.now();
    const evidence: Evidence[] = [];
    let score = 0;
    let passed = false;

    try {
      if (!context.page) {
        throw new Error('Browser page not available');
      }

      // Step 1: Use enhanced content discovery to find cookie banners
      const bannerResult = await EnhancedCheckTemplate.checkCookieConsentBanner(context);

      if (!bannerResult.found) {
        evidence.push({
          type: 'text',
          description: 'No cookie consent banner detected',
          value: 'No cookie banner elements found on the page',
        });
        score = 0;
        passed = false;
      } else {
        evidence.push({
          type: 'element',
          description: 'Cookie consent banner found',
          value: bannerResult.text.substring(0, 200), // Limit text length
        });
        score += 40; // Base score for having a banner

        // Step 2: Analyze banner content and compliance
        const bannerAnalysis = this.analyzeBannerContent(bannerResult.text);
        evidence.push(...bannerAnalysis.evidence);
        score += bannerAnalysis.score;

        // Step 3: Test consent interaction if available
        if (bannerResult.interactionResult) {
          const interactionAnalysis = this.analyzeConsentInteraction(
            bannerResult.interactionResult,
          );
          evidence.push(...interactionAnalysis.evidence);
          score += interactionAnalysis.score;
        }

        // Step 4: Check for granular consent options
        const granularityCheck = await this.checkConsentGranularity(context);
        evidence.push(...granularityCheck.evidence);
        score += granularityCheck.score;

        // Step 5: Verify cookie blocking before consent
        const blockingCheck = await this.checkCookieBlocking(context);
        evidence.push(...blockingCheck.evidence);
        score += blockingCheck.score;

        passed = score >= 70;
      }

      // Ensure score is within bounds
      score = Math.max(0, Math.min(100, score));

      return {
        passed,
        score,
        evidence,
        recommendations: this.generateRecommendations(bannerResult, score),
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        passed: false,
        score: 0,
        evidence: [
          {
            type: 'text',
            description: 'Cookie consent check failed',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: 'Fix website accessibility for cookie consent scanning',
            description:
              'Ensure the website loads properly and cookie consent mechanisms are accessible',
            implementation:
              'Check website loading, JavaScript functionality, and banner implementation',
            effort: 'moderate' as const,
            impact: 'high' as const,
          },
        ],
        executionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Analyze cookie banner content for GDPR compliance
   */
  private analyzeBannerContent(bannerText: string): { score: number; evidence: Evidence[] } {
    const patterns = [
      { name: 'Cookie information', regex: /cookie|cookies/i, weight: 10 },
      { name: 'Purpose explanation', regex: /purpose|why|use|collect|track/i, weight: 15 },
      { name: 'Consent request', regex: /consent|agree|accept|allow/i, weight: 15 },
      { name: 'Rejection option', regex: /reject|decline|deny|refuse|necessary only/i, weight: 20 },
      {
        name: 'More information link',
        regex: /more info|learn more|privacy policy|cookie policy/i,
        weight: 10,
      },
      {
        name: 'Granular choices',
        regex: /customize|settings|preferences|manage|choose/i,
        weight: 15,
      },
      {
        name: 'Third party mention',
        regex: /third party|partners|analytics|advertising/i,
        weight: 10,
      },
      { name: 'Data processing mention', regex: /process|processing|data|personal/i, weight: 5 },
    ];

    return EnhancedCheckTemplate.analyzeTextContent(bannerText, patterns);
  }

  /**
   * Analyze consent interaction results
   */
  private analyzeConsentInteraction(interactionResult: InteractionResult): {
    score: number;
    evidence: Evidence[];
  } {
    const evidence: Evidence[] = [];
    let score = 0;

    if (interactionResult.interactionSuccessful) {
      evidence.push({
        type: 'interaction',
        description: 'Consent interaction successful',
        value: 'Banner responded to user interaction',
      });
      score += 15;
    }

    if (interactionResult.consentChoices && interactionResult.consentChoices.length > 0) {
      const acceptChoices = interactionResult.consentChoices.filter(
        (c: ConsentChoice) => c.type === 'accept',
      );
      const rejectChoices = interactionResult.consentChoices.filter(
        (c: ConsentChoice) => c.type === 'reject',
      );

      if (acceptChoices.length > 0) {
        evidence.push({
          type: 'interaction',
          description: 'Accept consent option available',
          value: `${acceptChoices.length} accept options found`,
        });
        score += 10;
      }

      if (rejectChoices.length > 0) {
        evidence.push({
          type: 'interaction',
          description: 'Reject consent option available',
          value: `${rejectChoices.length} reject options found`,
        });
        score += 15; // Higher score for reject options (GDPR requirement)
      }
    }

    // Check cookie behavior
    const cookieIncrease =
      interactionResult.cookiesAfterConsent - interactionResult.cookiesBeforeConsent;
    if (cookieIncrease > 0) {
      evidence.push({
        type: 'cookie',
        description: 'Cookies set after consent',
        value: `${cookieIncrease} additional cookies set after interaction`,
      });
      score += 5;
    }

    return { score, evidence };
  }

  /**
   * Check for granular consent options
   */
  private async checkConsentGranularity(
    context: CheckExecutionContext,
  ): Promise<{ score: number; evidence: Evidence[] }> {
    const evidence: Evidence[] = [];
    let score = 0;

    if (!context.page) {
      return { score: 0, evidence: [] };
    }

    try {
      // Look for granular consent controls
      const granularSelectors = [
        'input[type="checkbox"]',
        'button[data-category]',
        '[class*="toggle"]',
        '[class*="switch"]',
        'select[name*="consent"]',
        '[data-purpose]',
        '[data-vendor]',
      ];

      let granularControls = 0;
      for (const selector of granularSelectors) {
        try {
          const elements = await context.page.$$(selector);
          granularControls += elements.length;
        } catch (error) {
          continue;
        }
      }

      if (granularControls > 0) {
        evidence.push({
          type: 'element',
          description: 'Granular consent controls found',
          value: `${granularControls} consent control elements detected`,
        });
        score += Math.min(20, granularControls * 5); // Up to 20 points for granular controls
      }

      // Check for consent categories
      const categoryKeywords = [
        'necessary',
        'functional',
        'analytics',
        'marketing',
        'advertising',
        'performance',
      ];
      const pageText = (await context.page.$eval('body', (el) => el.textContent || '')) || '';
      const foundCategories = categoryKeywords.filter((keyword) =>
        pageText.toLowerCase().includes(keyword),
      );

      if (foundCategories.length > 2) {
        evidence.push({
          type: 'text',
          description: 'Cookie categories mentioned',
          value: `Categories found: ${foundCategories.join(', ')}`,
        });
        score += 10;
      }
    } catch (error) {
      console.warn('Granularity check failed:', error);
    }

    return { score, evidence };
  }

  /**
   * Check if cookies are properly blocked before consent
   */
  private async checkCookieBlocking(
    context: CheckExecutionContext,
  ): Promise<{ score: number; evidence: Evidence[] }> {
    const evidence: Evidence[] = [];
    let score = 0;

    if (!context.page) {
      return { score: 0, evidence: [] };
    }

    try {
      // Get initial cookies (should be minimal)
      const browserContext = context.page.browserContext();
      const initialCookies = await browserContext.cookies();
      const nonEssentialCookies = initialCookies.filter(
        (cookie: { name: string }) => !this.isEssentialCookie(cookie.name),
      );

      if (nonEssentialCookies.length === 0) {
        evidence.push({
          type: 'cookie',
          description: 'No non-essential cookies before consent',
          value: 'Good practice - only essential cookies loaded initially',
        });
        score += 20;
      } else {
        evidence.push({
          type: 'cookie',
          description: 'Non-essential cookies found before consent',
          value: `${nonEssentialCookies.length} non-essential cookies detected`,
        });
        score -= 10; // Penalty for loading non-essential cookies without consent
      }

      // Check for tracking scripts
      const trackingScripts = await this.detectTrackingScripts(context);
      if (trackingScripts.length > 0) {
        evidence.push({
          type: 'script',
          description: 'Tracking scripts detected',
          value: `${trackingScripts.length} tracking scripts found: ${trackingScripts.join(', ')}`,
        });
        score -= 5; // Minor penalty for tracking scripts
      }
    } catch (error) {
      console.warn('Cookie blocking check failed:', error);
    }

    return { score, evidence };
  }

  /**
   * Check if a cookie is essential
   */
  private isEssentialCookie(cookieName: string): boolean {
    const essentialPatterns = [
      /^session/i,
      /^csrf/i,
      /^xsrf/i,
      /^auth/i,
      /^login/i,
      /^security/i,
      /^consent/i,
      /^gdpr/i,
      /^cookie.*notice/i,
    ];

    return essentialPatterns.some((pattern) => pattern.test(cookieName));
  }

  /**
   * Detect tracking scripts on the page
   */
  private async detectTrackingScripts(context: CheckExecutionContext): Promise<string[]> {
    if (!context.page) {
      return [];
    }

    try {
      const scripts = await context.page.$$eval('script[src]', (elements) =>
        elements.map((el) => el.getAttribute('src')).filter(Boolean),
      );

      const trackingDomains = [
        'google-analytics.com',
        'googletagmanager.com',
        'facebook.net',
        'doubleclick.net',
        'hotjar.com',
        'mixpanel.com',
        'segment.com',
        'amplitude.com',
      ];

      return scripts
        .filter((src) => trackingDomains.some((domain) => src!.includes(domain)))
        .map((src) => {
          const url = new URL(src!);
          return url.hostname;
        });
    } catch (error) {
      return [];
    }
  }

  /**
   * Generate recommendations based on check results
   */
  private generateRecommendations(
    bannerResult: { found: boolean; text: string },
    score: number,
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (!bannerResult.found) {
      recommendations.push({
        priority: 1,
        title: 'Implement cookie consent banner',
        description: 'Add a GDPR-compliant cookie consent banner to your website',
        implementation:
          'Install a cookie consent management platform or implement a custom banner with accept/reject options',
        effort: 'moderate' as const,
        impact: 'high' as const,
      });
    } else if (score < 70) {
      recommendations.push({
        priority: 1,
        title: 'Improve cookie consent compliance',
        description: 'Enhance your cookie consent mechanism to meet GDPR requirements',
        implementation: 'Add reject options, granular controls, and ensure proper cookie blocking',
        effort: 'moderate' as const,
        impact: 'high' as const,
      });
    }

    if (score < 50) {
      recommendations.push({
        priority: 2,
        title: 'Add granular consent options',
        description: 'Provide users with granular control over different cookie categories',
        implementation:
          'Implement category-based consent (necessary, functional, analytics, marketing)',
        effort: 'significant' as const,
        impact: 'high' as const,
      });
    }

    if (bannerResult.found && !bannerResult.text.toLowerCase().includes('reject')) {
      recommendations.push({
        priority: 2,
        title: 'Add reject option',
        description: 'Provide a clear option for users to reject non-essential cookies',
        implementation: 'Add a "Reject" or "Decline" button alongside the accept option',
        effort: 'minimal' as const,
        impact: 'high' as const,
      });
    }

    return recommendations;
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.checkTemplate.cleanup();
  }
}
