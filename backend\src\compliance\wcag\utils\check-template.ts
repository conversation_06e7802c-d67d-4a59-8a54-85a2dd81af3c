/**
 * WCAG Check Template System
 * Provides consistent structure for all WCAG checks
 */

import { Page } from 'puppeteer';
import { WcagCheckResult, WcagEvidence, WcagCategory, WcagVersion, WcagLevel, AutomatedCheckStatus, WcagManualReviewItem } from '../types';

export interface CheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
  page?: Page;
}

export interface EnhancedCheckConfig extends CheckConfig {
  retryAttempts: number;
  enableJavaScript: boolean;
  enableImages: boolean;
  followRedirects: boolean;
}

export type CheckFunction<T extends CheckConfig> = (
  page: Page,
  config: T
) => Promise<{
  score: number;
  maxScore: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
}>;

export class CheckTemplate {
  /**
   * Execute a WCAG check with consistent error handling and logging
   */
  async executeCheck<T extends CheckConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    config: T,
    checkFunction: CheckFunction<T>,
    requiresBrowser: boolean = true,
    requiresManualReview: boolean = false
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 [${config.scanId}] Starting ${ruleId}: ${ruleName}`);
      
      if (requiresManualReview) {
        throw new Error('Manual review checks should not use fully automated template');
      }
      
      try {
        if (requiresBrowser && !config.page) {
          throw new Error('Browser instance required - page not provided in config');
        }

        // Execute the specific check function
        const result = await checkFunction(config.page!, config);
        
        const executionTime = Date.now() - startTime;
        
        console.log(`✅ [${config.scanId}] Completed ${ruleId} in ${executionTime}ms`);
        
        return {
          ruleId,
          ruleName,
          category: category as WcagCategory,
          wcagVersion: this.getVersionFromRuleId(ruleId),
          successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
          level: level as WcagLevel,
          status: result.score === result.maxScore ? 'passed' : 'failed',
          score: result.score,
          maxScore: result.maxScore,
          weight,
          automated: true,
          evidence: result.evidence,
          recommendations: result.recommendations,
          executionTime
        };
        
      } finally {
        // Browser cleanup will be handled by orchestrator
      }
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ [${config.scanId}] Error in ${ruleId}:`, error);
      
      return {
        ruleId,
        ruleName,
        category: category as WcagCategory,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as WcagLevel,
        status: 'failed',
        score: 0,
        maxScore: 100,
        weight,
        automated: true,
        evidence: [],
        recommendations: ['Check failed due to technical error - manual review recommended'],
        executionTime,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get WCAG version from rule ID
   */
  protected getVersionFromRuleId(ruleId: string): WcagVersion {
    const ruleNumber = parseInt(ruleId.split('-')[1]);
    
    if (ruleNumber <= 9) return '2.1';
    if (ruleNumber <= 16) return '2.2';
    return '3.0';
  }

  /**
   * Get success criterion from rule ID
   */
  protected getSuccessCriterionFromRuleId(ruleId: string): string {
    const criterionMap: Record<string, string> = {
      'WCAG-001': '1.1.1',
      'WCAG-002': '1.2.2',
      'WCAG-003': '1.3.1',
      'WCAG-004': '1.4.3',
      'WCAG-005': '2.1.1',
      'WCAG-006': '2.4.3',
      'WCAG-007': '2.4.7',
      'WCAG-008': '3.3.1',
      'WCAG-009': '4.1.2',
      'WCAG-010': '2.4.11',
      'WCAG-011': '2.4.12',
      'WCAG-012': '2.4.13',
      'WCAG-013': '2.5.7',
      'WCAG-014': '2.5.8',
      'WCAG-015': '3.2.6',
      'WCAG-016': '3.3.7',
      'WCAG-017': '2.1',
      'WCAG-018': '2.2',
      'WCAG-019': '2.4',
      'WCAG-020': '2.5',
      'WCAG-021': '3.1'
    };
    
    return criterionMap[ruleId] || 'Unknown';
  }
}
