/**
 * GDPR Compliance Orchestrator
 *
 * This class coordinates all GDPR compliance checks and manages the scanning process.
 * It follows the same pattern as the HIPAA orchestrator for consistency.
 */

import {
  GdprScanRequest,
  GdprScanResult,
  GdprCheckResult,
  GdprRecommendation,
  RiskLevel,
  ScanStatus,
  GdprCategory,
  CategoryBreakdown,
} from './types';
import { GDPR_SCORING_WEIGHTS } from './constants';
import { GdprDatabase } from './database/gdpr-database';
import { BrowserPoolManager } from './utils/browser-pool';
import { v4 as uuidv4 } from 'uuid';

export class GdprOrchestrator {
  private browserPool: BrowserPoolManager;
  private static scanCache = new Map<string, { result: GdprScanResult; timestamp: number }>();
  private static readonly CACHE_TTL = 300000; // 5 minutes cache

  constructor() {
    this.browserPool = BrowserPoolManager.getInstance();
  }

  /**
   * Perform comprehensive GDPR compliance scan
   * REAL WEBSITE SCANNING ONLY - NO MOCK DATA
   */
  async performComprehensiveScan(
    userId: string,
    scanRequest: GdprScanRequest,
  ): Promise<GdprScanResult> {
    const scanId = uuidv4();
    const startTime = Date.now();

    console.log(`🔍 Starting GDPR compliance scan for: ${scanRequest.targetUrl}`);
    console.log(`📋 Scan ID: ${scanId}`);

    // Check cache for recent scan of the same URL (optional optimization)
    const cacheKey = `${scanRequest.targetUrl}:${JSON.stringify(scanRequest.scanOptions || {})}`;
    const cachedResult = GdprOrchestrator.scanCache.get(cacheKey);

    if (cachedResult && Date.now() - cachedResult.timestamp < GdprOrchestrator.CACHE_TTL) {
      console.log(
        `⚡ Using cached result for ${scanRequest.targetUrl} (${Math.round((Date.now() - cachedResult.timestamp) / 1000)}s old)`,
      );

      // Create a new scan record with cached data but new scan ID
      const cachedScanResult = {
        ...cachedResult.result,
        scanId,
        timestamp: new Date().toISOString(),
        scanDuration: Date.now() - startTime,
      };

      // Still save to database for tracking
      await GdprDatabase.createScan({
        scanId,
        userId,
        targetUrl: scanRequest.targetUrl,
        scanOptions: scanRequest.scanOptions || {},
      });

      await GdprDatabase.updateScanWithResults(scanId, cachedScanResult);
      await GdprDatabase.updateScanStatus(scanId, 'completed');

      return cachedScanResult;
    }

    try {
      // CRITICAL FIX: Create scan record FIRST before executing checks
      // This ensures foreign key constraints are satisfied when checks save data
      console.log(`📝 Creating scan record before executing checks...`);

      // Enhanced user validation and creation
      try {
        await GdprDatabase.createScan({
          userId,
          targetUrl: scanRequest.targetUrl,
          scanOptions: scanRequest.scanOptions || {},
          scanId, // Use the pre-generated scanId
        });
      } catch (createScanError: any) {
        if (
          createScanError.code === '23503' &&
          createScanError.constraint === 'gdpr_scans_user_id_fkey'
        ) {
          console.log(`⚠️ User ${userId} does not exist, attempting to create anonymous user...`);

          // Try to create the anonymous user
          const knex = require('../../lib/db').default;
          try {
            await knex('users').insert({
              id: userId,
              keycloak_id: 'anonymous-user-keycloak-id',
              email: '<EMAIL>',
              created_at: new Date(),
              updated_at: new Date(),
            });
            console.log(`✅ Created anonymous user: ${userId}`);

            // Retry creating the scan
            await GdprDatabase.createScan({
              userId,
              targetUrl: scanRequest.targetUrl,
              scanOptions: scanRequest.scanOptions || {},
              scanId,
            });
            console.log(`✅ Scan record created after user creation`);
          } catch (userCreateError: any) {
            if (userCreateError.code === '23505') {
              // User already exists, retry scan creation
              console.log(`💡 User already exists, retrying scan creation...`);
              await GdprDatabase.createScan({
                userId,
                targetUrl: scanRequest.targetUrl,
                scanOptions: scanRequest.scanOptions || {},
                scanId,
              });
            } else {
              console.error(`❌ Failed to create anonymous user:`, userCreateError);
              throw new Error(
                `Cannot create scan: user ${userId} does not exist and cannot be created`,
              );
            }
          }
        } else {
          throw createScanError;
        }
      }

      // Update status to running
      await GdprDatabase.updateScanStatus(scanId, 'running');

      // REAL SCANNING IMPLEMENTATION - Execute all 21 GDPR checks
      const checks = await this.executeAllGdprChecks(scanRequest, scanId);

      // Calculate overall score using dual scoring system (80% automated + 20% manual)
      const scoringResult = this.calculateDualGdprScore(checks);

      const scanDuration = Date.now() - startTime;

      // REAL SCAN RESULT - populated with actual check results
      const result: GdprScanResult = {
        scanId,
        targetUrl: scanRequest.targetUrl,
        timestamp: new Date().toISOString(),
        scanDuration,
        overallScore: scoringResult.overallScore,
        riskLevel: scoringResult.riskLevel,
        status: 'completed',
        summary: {
          totalChecks: checks.length,
          passedChecks: scoringResult.summary.passedChecks,
          failedChecks: scoringResult.summary.failedChecks,
          manualReviewRequired: scoringResult.summary.manualReviewRequired,
          criticalFailures: scoringResult.criticalFailures,
          categoryBreakdown: scoringResult.breakdown,
        },
        checks,
        recommendations: this.generateRecommendations(checks),
        metadata: {
          version: '1.0.0',
          processingTime: scanDuration,
          checksPerformed: checks.length,
          analysisLevelsUsed: ['pattern', 'behavioral', 'content', 'network'],
          errors: checks.filter((c) => !c.passed).map((c) => `${c.ruleName}: Failed`),
          warnings: checks
            .filter((c) => c.manualReviewRequired)
            .map((c) => `${c.ruleName}: Manual review required`),
          userAgent: scanRequest.scanOptions?.userAgent || 'GDPR-Compliance-Scanner/1.0',
          scanOptions: scanRequest.scanOptions,
        },
      };

      // Update existing scan record with final results
      await GdprDatabase.updateScanWithResults(scanId, result);
      await GdprDatabase.updateScanStatus(scanId, 'completed');

      // Cache the result for future scans (performance optimization)
      const cacheKey = `${scanRequest.targetUrl}:${JSON.stringify(scanRequest.scanOptions || {})}`;
      GdprOrchestrator.scanCache.set(cacheKey, {
        result,
        timestamp: Date.now(),
      });

      // Clean up old cache entries (keep cache size manageable)
      if (GdprOrchestrator.scanCache.size > 100) {
        const oldestEntries = Array.from(GdprOrchestrator.scanCache.entries())
          .sort(([, a], [, b]) => a.timestamp - b.timestamp)
          .slice(0, 50);

        oldestEntries.forEach(([key]) => GdprOrchestrator.scanCache.delete(key));
        console.log(`🧹 Cleaned up ${oldestEntries.length} old cache entries`);
      }

      console.log(`✅ GDPR scan completed successfully: ${scanId} (cached for future use)`);
      return result;
    } catch (error) {
      console.error(`❌ GDPR scan failed: ${scanId}`, error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await GdprDatabase.updateScanStatus(scanId, 'failed', errorMessage);

      throw new Error(`GDPR scan failed: ${errorMessage}`);
    } finally {
      // Cleanup browser pool after scan
      await this.browserPool.cleanupIdleBrowsers();
    }
  }

  /**
   * Cleanup all browser resources
   * Call this when shutting down the application
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up GDPR orchestrator resources');
    await this.browserPool.cleanup();
  }

  /**
   * Create a failed check result for error handling
   */
  private createFailedCheckResult(ruleId: string, ruleName: string, error: any): GdprCheckResult {
    return {
      ruleId: ruleId as any,
      ruleName,
      category: 'technical' as any,
      passed: false,
      score: 0,
      weight: 1,
      severity: 'medium' as any,
      manualReviewRequired: false,
      evidence: [
        {
          type: 'text',
          description: 'Check execution failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        },
      ],
      recommendations: [
        {
          priority: 1,
          title: 'Check Execution Error',
          description: 'This check could not be completed due to a technical error.',
          implementation: 'Review the error logs and retry the scan.',
          effort: 'minimal',
          impact: 'medium',
        },
      ],
    };
  }

  /**
   * Execute all 21 GDPR compliance checks
   * REAL IMPLEMENTATION - calls actual check classes
   */
  private async executeAllGdprChecks(
    scanRequest: GdprScanRequest,
    scanId: string,
  ): Promise<GdprCheckResult[]> {
    const checks: GdprCheckResult[] = [];
    const config = {
      targetUrl: scanRequest.targetUrl,
      timeout: scanRequest.scanOptions?.timeout || 300000,
      scanId, // Pass the real scan ID to prevent UUID errors
    };

    try {
      // Import all check classes
      const {
        HttpsTlsCheck,
        PrivacyPolicyCheck,
        PrivacyContentCheck,
        CookieConsentCheck,
        CookieClassificationCheck,
        TrackerDetectionCheck,
        CookieAttributesCheck,
        GpcDntCheck,
        FormConsentCheck,
        SecurityHeadersCheck,
        IpAnonymizationCheck,
        DataRightsCheck,
        SpecialDataCheck,
        ChildrenConsentCheck,
        DpoContactCheck,
        DataTransfersCheck,
        BreachNotificationCheck,
        DpiaCheck,
        DataRetentionCheck,
        ProcessorAgreementsCheck,
        ImprintContactCheck,
      } = await import('./checks');

      // OPTIMIZED: Hybrid execution - parallel for non-browser checks, sequential for browser checks
      console.log(`🔍 Executing GDPR compliance checks for: ${config.targetUrl}`);
      console.log(`🚀 Using HYBRID execution: parallel non-browser + sequential browser checks`);

      const nonBrowserChecks = [
        {
          name: 'HTTPS/TLS Security',
          class: HttpsTlsCheck,
          requiresBrowser: false,
          ruleId: 'GDPR-001',
        },
        {
          name: 'Security Headers',
          class: SecurityHeadersCheck,
          requiresBrowser: false,
          ruleId: 'GDPR-011',
        },
        {
          name: 'IP Anonymization',
          class: IpAnonymizationCheck,
          requiresBrowser: false,
          ruleId: 'GDPR-012',
        },
      ];

      const browserChecks = [
        {
          name: 'Privacy Policy Presence',
          class: PrivacyPolicyCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-002',
        },
        {
          name: 'Privacy Policy Content',
          class: PrivacyContentCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-003',
        },
        {
          name: 'Data Subject Rights',
          class: DataRightsCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-013',
        },
        {
          name: 'Cookie Consent Banner',
          class: CookieConsentCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-004',
        },
        {
          name: 'Cookie Classification',
          class: CookieClassificationCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-005',
        },
        {
          name: 'Cookie Attributes',
          class: CookieAttributesCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-007',
        },
        { name: 'GPC/DNT Support', class: GpcDntCheck, requiresBrowser: true, ruleId: 'GDPR-008' },
        {
          name: 'Tracker Detection',
          class: TrackerDetectionCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-006',
        },
        {
          name: 'Form Consent Controls',
          class: FormConsentCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-010',
        },
        {
          name: 'Special Category Data',
          class: SpecialDataCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-014',
        },
        {
          name: 'Children Consent',
          class: ChildrenConsentCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-015',
        },
        {
          name: 'DPO Contact Information',
          class: DpoContactCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-016',
        },
        {
          name: 'International Data Transfers',
          class: DataTransfersCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-018',
        },
        {
          name: 'Breach Notification',
          class: BreachNotificationCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-019',
        },
        {
          name: 'Data Protection Impact Assessment',
          class: DpiaCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-020',
        },
        {
          name: 'Data Retention Policies',
          class: DataRetentionCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-021',
        },
        {
          name: 'Processor Agreements',
          class: ProcessorAgreementsCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-017',
        },
        {
          name: 'Imprint Contact Details',
          class: ImprintContactCheck,
          requiresBrowser: true,
          ruleId: 'GDPR-009',
        },
      ];

      // Phase 1: Execute non-browser checks in parallel (fast network checks)
      console.log(
        `🔧 Phase 1: Executing ${nonBrowserChecks.length} non-browser checks in parallel...`,
      );
      const nonBrowserPromises = nonBrowserChecks.map(async (checkInfo, index) => {
        const progress = `${index + 1}/${nonBrowserChecks.length}`;
        console.log(`🔧 [${progress}] Starting: ${checkInfo.name}`);

        try {
          const checkInstance = new checkInfo.class();
          const result = await checkInstance.performCheck(config);

          const statusIcon = result.passed ? '✅' : '❌';
          const score = result.score ? ` (${result.score}%)` : '';
          console.log(`${statusIcon} [${progress}] Completed: ${checkInfo.name}${score}`);

          return result;
        } catch (error) {
          console.error(
            `❌ [${progress}] Failed: ${checkInfo.name} - ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          return this.createFailedCheckResult(checkInfo.ruleId, checkInfo.name, error);
        }
      });

      const nonBrowserResults = await Promise.allSettled(nonBrowserPromises);
      nonBrowserResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          checks.push(result.value);
        }
      });

      console.log(`✅ Phase 1 completed: ${nonBrowserResults.length} non-browser checks finished`);

      // Phase 2: Execute browser checks sequentially (resource-intensive)
      console.log(`🌐 Phase 2: Executing ${browserChecks.length} browser checks sequentially...`);
      for (let i = 0; i < browserChecks.length; i++) {
        const checkInfo = browserChecks[i];
        const progress = `${i + 1}/${browserChecks.length}`;

        console.log(`🌐 [${progress}] Starting: ${checkInfo.name}`);

        try {
          const checkInstance = new checkInfo.class();
          const result = await checkInstance.performCheck(config);
          checks.push(result);

          const statusIcon = result.passed ? '✅' : '❌';
          const score = result.score ? ` (${result.score}%)` : '';
          console.log(`${statusIcon} [${progress}] Completed: ${checkInfo.name}${score}`);
        } catch (error) {
          console.error(
            `❌ [${progress}] Failed: ${checkInfo.name} - ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          checks.push(this.createFailedCheckResult(checkInfo.ruleId, checkInfo.name, error));
        }

        // Small delay between browser checks to prevent resource exhaustion
        if (i < browserChecks.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }

      console.log(`✅ Completed ${checks.length} GDPR checks`);
      return checks;
    } catch (error) {
      console.error('❌ Failed to execute GDPR checks:', error);
      throw new Error(
        `Check execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Calculate GDPR compliance score using dual scoring system (80% automated + 20% manual)
   */
  private calculateDualGdprScore(checks: GdprCheckResult[]): {
    overallScore: number;
    riskLevel: RiskLevel;
    criticalFailures: number;
    breakdown: CategoryBreakdown[];
    summary: {
      passedChecks: number;
      failedChecks: number;
      manualReviewRequired: number;
    };
  } {
    const weights = GDPR_SCORING_WEIGHTS;

    // Manual review rules (7 total) - based on automated: false in constants
    const manualReviewRules = [
      'GDPR-003',
      'GDPR-010',
      'GDPR-013',
      'GDPR-014',
      'GDPR-015',
      'GDPR-018',
      'GDPR-020',
    ];

    // Separate automated and manual checks
    const automatedChecks = checks.filter((check) => !manualReviewRules.includes(check.ruleId));
    const manualChecks = checks.filter((check) => manualReviewRules.includes(check.ruleId));

    console.log(
      `📊 Dual scoring: ${automatedChecks.length} automated, ${manualChecks.length} manual checks`,
    );

    // Calculate Automated Score (14 checks)
    let automatedWeightedScore = 0;
    let automatedTotalWeight = 0;
    let automatedPassedChecks = 0;
    let automatedCriticalFailures = 0;

    for (const check of automatedChecks) {
      const weight = weights[check.ruleId] || 1;
      const score = check.passed ? 100 : 0;

      automatedWeightedScore += score * weight;
      automatedTotalWeight += weight;

      if (check.passed) automatedPassedChecks++;
      if (!check.passed && weight >= 7) automatedCriticalFailures++;
    }

    const automatedBaseScore =
      automatedTotalWeight > 0 ? automatedWeightedScore / automatedTotalWeight : 0;
    const automatedPenalty = automatedCriticalFailures * 5; // Reduced penalty for automated only
    const automatedScore = Math.max(0, automatedBaseScore - automatedPenalty);

    // Calculate Manual Score (7 checks) - all pending initially, so 0%
    const manualScore = 0; // No manual reviews completed yet

    // Overall Score Calculation: (Automated Score × 0.8) + (Manual Score × 0.2)
    const overallScore = automatedScore * 0.8 + manualScore * 0.2;

    // Count total stats
    const totalPassedChecks = automatedPassedChecks;
    const totalManualReviewRequired = manualChecks.length;
    const totalCriticalFailures = automatedCriticalFailures;

    console.log('📊 Dual score calculation details:');
    console.log(`   Automated checks: ${automatedChecks.length}, passed: ${automatedPassedChecks}`);
    console.log(`   Manual checks: ${manualChecks.length}, pending: ${totalManualReviewRequired}`);
    console.log(`   Automated score: ${Math.round(automatedScore)}%`);
    console.log(`   Manual score: ${Math.round(manualScore)}% (pending reviews)`);
    console.log(`   Overall score: ${Math.round(overallScore)}% (80% auto + 20% manual)`);
    console.log(`   Critical failures: ${totalCriticalFailures}`);

    return {
      overallScore: Math.round(overallScore),
      riskLevel: this.determineRiskLevel(overallScore, totalCriticalFailures),
      criticalFailures: totalCriticalFailures,
      breakdown: this.generateCategoryBreakdown(checks),
      summary: {
        passedChecks: totalPassedChecks,
        failedChecks: checks.length - totalPassedChecks,
        manualReviewRequired: totalManualReviewRequired,
      },
    };
  }

  /**
   * Determine risk level based on score and critical failures
   */
  private determineRiskLevel(score: number, criticalFailures: number): RiskLevel {
    if (criticalFailures >= 3 || score < 40) return 'critical';
    if (criticalFailures >= 2 || score < 60) return 'high';
    if (criticalFailures >= 1 || score < 75) return 'medium';
    return 'low';
  }

  /**
   * Generate category breakdown for scoring
   */
  private generateCategoryBreakdown(checks: GdprCheckResult[]): CategoryBreakdown[] {
    const categoryMap = new Map<
      GdprCategory,
      {
        totalChecks: number;
        passedChecks: number;
      }
    >();

    for (const check of checks) {
      const existing = categoryMap.get(check.category) || {
        totalChecks: 0,
        passedChecks: 0,
      };

      existing.totalChecks++;
      if (check.passed) existing.passedChecks++;

      categoryMap.set(check.category, existing);
    }

    return Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      score: data.totalChecks > 0 ? Math.round((data.passedChecks / data.totalChecks) * 100) : 0,
      checksInCategory: data.totalChecks,
      passedInCategory: data.passedChecks,
    }));
  }

  /**
   * Generate recommendations based on check results
   */
  private generateRecommendations(checks: GdprCheckResult[]): GdprRecommendation[] {
    const recommendations: GdprRecommendation[] = [];
    let recommendationId = 1;

    for (const check of checks) {
      if (!check.passed && check.recommendations.length > 0) {
        for (const rec of check.recommendations) {
          recommendations.push({
            id: `REC-${recommendationId++}`,
            priority: rec.priority,
            title: rec.title,
            description: rec.description,
            category: check.category,
            effort: rec.effort,
            impact: rec.impact,
            timeline: this.getTimelineFromEffort(rec.effort),
            relatedRules: [check.ruleId],
          });
        }
      }
    }

    // Sort by priority
    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get timeline estimate based on effort level
   */
  private getTimelineFromEffort(effort: string): string {
    switch (effort) {
      case 'minimal':
        return '1-2 days';
      case 'moderate':
        return '1-2 weeks';
      case 'significant':
        return '2-4 weeks';
      default:
        return '1-2 weeks';
    }
  }

  /**
   * Get scan status
   */
  async getScanStatus(scanId: string): Promise<ScanStatus | null> {
    try {
      const result = await GdprDatabase.getScanResult(scanId);
      return result?.status || null;
    } catch (error) {
      console.error('❌ Failed to get scan status:', error);
      return null;
    }
  }
}
