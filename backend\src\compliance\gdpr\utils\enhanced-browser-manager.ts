import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, HTTPResponse } from 'puppeteer';
import { BrowserPoolManager } from './browser-pool';
import { SmartUrlResolver, UrlResolutionOptions } from './smart-url-resolver';

export interface BrowserNavigationOptions {
  timeout?: number;
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle0' | 'networkidle2';
  userAgent?: string;
  viewport?: { width: number; height: number };
  enableJavaScript?: boolean;
  enableImages?: boolean;
  enableCSS?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
  bypassCSP?: boolean;
  blockResources?: string[];
}

export interface PageAnalysisResult {
  url: string;
  title: string;
  content: string;
  links: Array<{ href: string; text: string }>;
  forms: Array<{ action: string; method: string; fields: string[] }>;
  cookies: Array<{ name: string; value: string; domain: string; path: string }>;
  scripts: string[];
  stylesheets: string[];
  images: string[];
  metadata: Record<string, string>;
  performance: {
    loadTime: number;
    domContentLoadedTime: number;
    networkRequests: number;
    resourceSizes: Record<string, number>;
  };
}

export interface ConsentInteractionResult {
  bannerFound: boolean;
  bannerText: string;
  interactionSuccessful: boolean;
  cookiesBeforeConsent: number;
  cookiesAfterConsent: number;
  consentChoices: Array<{
    type: 'accept' | 'reject' | 'customize';
    selector: string;
    clicked: boolean;
  }>;
}

/**
 * Enhanced Browser Manager for GDPR compliance scanning
 * Provides intelligent browser automation with anti-detection and error recovery
 */
export class EnhancedBrowserManager {
  private browserPool: BrowserPoolManager;
  private urlResolver: SmartUrlResolver;

  constructor() {
    this.browserPool = BrowserPoolManager.getInstance();
    this.urlResolver = new SmartUrlResolver();
  }

  /**
   * Navigate to a URL with enhanced error handling and anti-detection
   */
  async navigateToUrl(
    url: string,
    options: BrowserNavigationOptions = {},
  ): Promise<{ page: Page; response: HTTPResponse | null; browser: Browser }> {
    const browser = await this.browserPool.getBrowser();
    let page: Page | null = null;
    let response: HTTPResponse | null = null;

    try {
      page = await browser.newPage();

      // Configure page settings
      await this.configurePage(page, options);

      // Attempt navigation with retry logic
      response = await this.attemptNavigation(page, url, options);

      return { page, response, browser };
    } catch (error) {
      // Cleanup on failure
      if (page) {
        await page.close().catch(() => {});
      }
      await this.browserPool.releaseBrowser(browser);
      throw error;
    }
  }

  /**
   * Perform comprehensive page analysis
   */
  async analyzePage(page: Page): Promise<PageAnalysisResult> {
    const startTime = Date.now();

    try {
      // Wait for page to be ready
      await page.waitForSelector('body', { timeout: 10000 });

      // Extract page information
      const analysis = await page.evaluate(() => {
        const result = {
          url: window.location.href,
          title: document.title,
          content: document.body.textContent || '',
          links: [] as Array<{ href: string; text: string }>,
          forms: [] as Array<{ action: string; method: string; fields: string[] }>,
          scripts: [] as string[],
          stylesheets: [] as string[],
          images: [] as string[],
          metadata: {} as Record<string, string>,
        };

        // Extract links
        const linkElements = document.querySelectorAll('a[href]');
        for (const link of linkElements) {
          const href = link.getAttribute('href');
          const text = link.textContent?.trim();
          if (href && text) {
            result.links.push({ href, text });
          }
        }

        // Extract forms
        const formElements = document.querySelectorAll('form');
        for (const form of formElements) {
          const action = form.getAttribute('action') || '';
          const method = form.getAttribute('method') || 'GET';
          const fields: string[] = [];

          const inputs = form.querySelectorAll('input, select, textarea');
          for (const input of inputs) {
            const name = input.getAttribute('name');
            if (name) fields.push(name);
          }

          result.forms.push({ action, method, fields });
        }

        // Extract scripts
        const scriptElements = document.querySelectorAll('script[src]');
        for (const script of scriptElements) {
          const src = script.getAttribute('src');
          if (src) result.scripts.push(src);
        }

        // Extract stylesheets
        const linkElements2 = document.querySelectorAll('link[rel="stylesheet"]');
        for (const link of linkElements2) {
          const href = link.getAttribute('href');
          if (href) result.stylesheets.push(href);
        }

        // Extract images
        const imgElements = document.querySelectorAll('img[src]');
        for (const img of imgElements) {
          const src = img.getAttribute('src');
          if (src) result.images.push(src);
        }

        // Extract metadata
        const metaElements = document.querySelectorAll('meta[name], meta[property]');
        for (const meta of metaElements) {
          const name = meta.getAttribute('name') || meta.getAttribute('property');
          const content = meta.getAttribute('content');
          if (name && content) {
            result.metadata[name] = content;
          }
        }

        return result;
      });

      // Get cookies
      const cookies = await page.cookies();

      // Get performance metrics
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType(
          'navigation',
        )[0] as PerformanceNavigationTiming;
        return {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoadedTime:
            navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          networkRequests: performance.getEntriesByType('resource').length,
        };
      });

      return {
        ...analysis,
        cookies: cookies.map((cookie) => ({
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain,
          path: cookie.path,
        })),
        performance: {
          ...performanceMetrics,
          resourceSizes: {}, // Could be enhanced to track resource sizes
        },
      };
    } catch (error) {
      console.warn('Page analysis failed:', error);

      // Return minimal analysis on failure
      return {
        url: page.url(),
        title: await page.title().catch(() => ''),
        content: (await page.$eval('body', (el) => el.textContent || '').catch(() => '')) || '',
        links: [],
        forms: [],
        cookies: [],
        scripts: [],
        stylesheets: [],
        images: [],
        metadata: {},
        performance: {
          loadTime: Date.now() - startTime,
          domContentLoadedTime: 0,
          networkRequests: 0,
          resourceSizes: {},
        },
      };
    }
  }

  /**
   * Interact with cookie consent banners
   */
  async interactWithConsentBanner(page: Page): Promise<ConsentInteractionResult> {
    const result: ConsentInteractionResult = {
      bannerFound: false,
      bannerText: '',
      interactionSuccessful: false,
      cookiesBeforeConsent: 0,
      cookiesAfterConsent: 0,
      consentChoices: [],
    };

    try {
      // Count cookies before interaction
      const cookiesBefore = await page.cookies();
      result.cookiesBeforeConsent = cookiesBefore.length;

      // Wait for potential banner to appear
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Look for cookie banners using common selectors
      const bannerSelectors = [
        '#onetrust-banner-sdk',
        '.ot-sdk-container',
        '#cookieChoiceInfo',
        '.cc-banner',
        '.cookie-banner',
        '.consent-banner',
        '.gdpr-banner',
        '[id*="cookie"]',
        '[class*="cookie"]',
        '[id*="consent"]',
        '[class*="consent"]',
      ];

      let bannerElement = null;
      for (const selector of bannerSelectors) {
        try {
          bannerElement = await page.$(selector);
          if (bannerElement) {
            result.bannerFound = true;
            result.bannerText = (await bannerElement.evaluate((el) => el.textContent || '')) || '';
            break;
          }
        } catch (error) {
          continue;
        }
      }

      if (result.bannerFound && bannerElement) {
        // Try to find and click consent buttons
        const buttonSelectors = [
          'button:has-text("Accept")',
          'button:has-text("Allow")',
          'button:has-text("Agree")',
          'button:has-text("OK")',
          'button:has-text("Akzeptieren")',
          'button:has-text("Zustimmen")',
          '[data-testid*="accept"]',
          '[data-cy*="accept"]',
          '.accept-button',
          '.consent-accept',
        ];

        for (const selector of buttonSelectors) {
          try {
            const button = await page.$(selector);
            if (button) {
              await button.click();
              result.consentChoices.push({
                type: 'accept',
                selector,
                clicked: true,
              });
              result.interactionSuccessful = true;
              break;
            }
          } catch (error) {
            result.consentChoices.push({
              type: 'accept',
              selector,
              clicked: false,
            });
          }
        }

        // Wait for banner to disappear or page to update
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      // Count cookies after interaction
      const cookiesAfter = await page.cookies();
      result.cookiesAfterConsent = cookiesAfter.length;
    } catch (error) {
      console.warn('Consent banner interaction failed:', error);
    }

    return result;
  }

  /**
   * Configure page with anti-detection and performance settings
   */
  private async configurePage(page: Page, options: BrowserNavigationOptions): Promise<void> {
    try {
      // Set viewport
      if (options.viewport) {
        await page.setViewport(options.viewport);
      } else {
        await page.setViewport({ width: 1920, height: 1080 });
      }

      // Set user agent
      if (options.userAgent) {
        await page.setUserAgent(options.userAgent);
      }

      // Block unnecessary resources for performance (simplified)
      if (options.enableImages === false || options.enableCSS === false) {
        await page.setRequestInterception(true);
        page.on('request', (request) => {
          const resourceType = request.resourceType();

          // Block images if disabled
          if (options.enableImages === false && resourceType === 'image') {
            request.abort();
            return;
          }

          // Block CSS if disabled
          if (options.enableCSS === false && resourceType === 'stylesheet') {
            request.abort();
            return;
          }

          request.continue();
        });
      }

      // Set extra headers for better compatibility
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        'Upgrade-Insecure-Requests': '1',
      });

      // Bypass CSP if requested
      if (options.bypassCSP) {
        await page.setBypassCSP(true);
      }

      // Enable/disable JavaScript
      if (options.enableJavaScript === false) {
        await page.setJavaScriptEnabled(false);
      }
    } catch (error) {
      console.warn('Page configuration failed:', error);
      // Continue without advanced configuration
    }
  }

  /**
   * Attempt navigation with retry logic
   */
  private async attemptNavigation(
    page: Page,
    url: string,
    options: BrowserNavigationOptions,
  ): Promise<HTTPResponse | null> {
    const maxAttempts = options.retryAttempts || 3;
    const retryDelay = options.retryDelay || 2000;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await page.goto(url, {
          timeout: options.timeout || 30000,
          waitUntil: options.waitUntil || 'domcontentloaded',
        });

        return response;
      } catch (error) {
        console.warn(`Navigation attempt ${attempt}/${maxAttempts} failed for ${url}: ${error}`);

        if (attempt === maxAttempts) {
          throw error;
        }

        // Wait before retry
        await new Promise((resolve) => setTimeout(resolve, retryDelay * attempt));
      }
    }

    return null;
  }

  /**
   * Release browser resources
   */
  async releaseBrowser(browser: Browser): Promise<void> {
    await this.browserPool.releaseBrowser(browser);
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    await this.browserPool.cleanup();
  }
}
