/**
 * <PERSON><PERSON><PERSON> to check and run WCAG migration if needed
 */

import knex from 'knex';
import config from '../knexfile';
import { env } from '../../lib/env';

async function checkAndRunWcagMigration() {
  const db = knex(config.development);
  
  try {
    // Check if WCAG tables exist
    const wcagTablesExist = await db.schema.hasTable('wcag_scans');
    
    if (wcagTablesExist) {
      console.log('✅ WCAG tables already exist');
      
      // Check if migration is recorded
      const migrationExists = await db('knex_migrations')
        .where('name', '20250701000001_create_wcag_tables.ts')
        .first();
        
      if (!migrationExists) {
        console.log('📝 Recording WCAG migration as completed...');
        await db('knex_migrations').insert({
          name: '20250701000001_create_wcag_tables.ts',
          batch: 1,
          migration_time: new Date()
        });
        console.log('✅ WCAG migration recorded');
      } else {
        console.log('✅ WCAG migration already recorded');
      }
    } else {
      console.log('🔄 Running WCAG migration...');
      
      // Import and run the WCAG migration directly
      const wcagMigration = await import('../../migrations/20250701000001_create_wcag_tables');
      await wcagMigration.up(db);
      
      // Record the migration
      await db('knex_migrations').insert({
        name: '20250701000001_create_wcag_tables.ts',
        batch: 1,
        migration_time: new Date()
      });
      
      console.log('✅ WCAG migration completed');
    }
    
    // Verify all WCAG tables exist
    const tables = ['wcag_scans', 'wcag_automated_results', 'wcag_contrast_analysis', 'wcag_focus_analysis', 'wcag_keyboard_analysis', 'wcag_manual_reviews'];
    
    for (const table of tables) {
      const exists = await db.schema.hasTable(table);
      console.log(`${exists ? '✅' : '❌'} Table ${table}: ${exists ? 'exists' : 'missing'}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await db.destroy();
  }
}

checkAndRunWcagMigration();
