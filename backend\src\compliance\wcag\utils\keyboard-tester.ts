/**
 * Keyboard Accessibility Testing Utilities
 * Provides automated keyboard navigation and interaction testing
 */

import { Page } from 'puppeteer';
import { KeyboardAnalysisResult } from '../types';

export interface KeyboardTestResult {
  canNavigateWithTab: boolean;
  canActivateWithEnter: boolean;
  canActivateWithSpace: boolean;
  canEscapeWithEscape: boolean;
  supportsArrowKeys: boolean;
  hasKeyboardShortcuts: boolean;
  issues: string[];
}

export interface InteractiveElement {
  selector: string;
  tagName: string;
  role?: string;
  type?: string;
  isButton: boolean;
  isLink: boolean;
  isInput: boolean;
  isCustomControl: boolean;
  expectedKeys: string[];
}

export interface KeyboardNavigationResult {
  totalElements: number;
  accessibleElements: number;
  inaccessibleElements: string[];
  navigationIssues: string[];
  shortcutConflicts: string[];
}

export class KeyboardTester {
  /**
   * Test keyboard accessibility for all interactive elements
   */
  static async testKeyboardAccessibility(page: Page): Promise<KeyboardNavigationResult> {
    const interactiveElements = await this.getInteractiveElements(page);
    const results: KeyboardTestResult[] = [];
    const inaccessibleElements: string[] = [];
    const navigationIssues: string[] = [];
    
    for (const element of interactiveElements) {
      try {
        const testResult = await this.testElementKeyboardSupport(page, element);
        results.push(testResult);
        
        if (!testResult.canNavigateWithTab) {
          inaccessibleElements.push(`${element.selector} - Cannot navigate with Tab`);
        }
        
        if (element.isButton && !testResult.canActivateWithEnter && !testResult.canActivateWithSpace) {
          inaccessibleElements.push(`${element.selector} - Button cannot be activated with keyboard`);
        }
        
        if (element.isLink && !testResult.canActivateWithEnter) {
          inaccessibleElements.push(`${element.selector} - Link cannot be activated with Enter`);
        }
        
        navigationIssues.push(...testResult.issues);
      } catch (error) {
        console.error(`Error testing element ${element.selector}:`, error);
        inaccessibleElements.push(`${element.selector} - Testing error`);
      }
    }
    
    // Test for keyboard shortcut conflicts
    const shortcutConflicts = await this.detectShortcutConflicts(page);
    
    return {
      totalElements: interactiveElements.length,
      accessibleElements: interactiveElements.length - inaccessibleElements.length,
      inaccessibleElements,
      navigationIssues,
      shortcutConflicts
    };
  }

  /**
   * Get all interactive elements that should support keyboard navigation
   */
  private static async getInteractiveElements(page: Page): Promise<InteractiveElement[]> {
    return await page.evaluate(() => {
      const elements: InteractiveElement[] = [];
      
      // Standard interactive elements
      const interactiveSelectors = [
        'button',
        'a[href]',
        'input:not([type="hidden"])',
        'select',
        'textarea',
        '[role="button"]',
        '[role="link"]',
        '[role="menuitem"]',
        '[role="tab"]',
        '[role="checkbox"]',
        '[role="radio"]',
        '[role="slider"]',
        '[role="spinbutton"]',
        '[tabindex="0"]',
        '[onclick]'
      ];
      
      interactiveSelectors.forEach(selector => {
        const nodeList = document.querySelectorAll(selector);
        nodeList.forEach((element, index) => {
          const rect = element.getBoundingClientRect();
          const isVisible = rect.width > 0 && rect.height > 0;
          
          if (isVisible) {
            const tagName = element.tagName.toLowerCase();
            const role = element.getAttribute('role');
            const type = (element as HTMLInputElement).type;
            
            elements.push({
              selector: `${selector}:nth-of-type(${index + 1})`,
              tagName,
              role: role || undefined,
              type: type || undefined,
              isButton: tagName === 'button' || role === 'button',
              isLink: tagName === 'a' || role === 'link',
              isInput: ['input', 'select', 'textarea'].includes(tagName),
              isCustomControl: !!role && !['button', 'link'].includes(role),
              expectedKeys: this.getExpectedKeys(tagName, role || undefined, type || undefined)
            });
          }
        });
      });
      
      return elements;
    });
  }

  /**
   * Get expected keyboard support for element type
   */
  private static getExpectedKeys(tagName: string, role?: string, type?: string): string[] {
    const keys: string[] = ['Tab']; // All interactive elements should support Tab
    
    if (tagName === 'button' || role === 'button') {
      keys.push('Enter', 'Space');
    } else if (tagName === 'a' || role === 'link') {
      keys.push('Enter');
    } else if (tagName === 'input') {
      keys.push('Enter');
      if (type === 'checkbox' || type === 'radio') {
        keys.push('Space');
      }
    } else if (role === 'tab') {
      keys.push('Enter', 'Space', 'ArrowLeft', 'ArrowRight');
    } else if (role === 'menuitem') {
      keys.push('Enter', 'Space', 'ArrowUp', 'ArrowDown');
    } else if (role === 'slider') {
      keys.push('ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End');
    }
    
    return keys;
  }

  /**
   * Test keyboard support for a specific element
   */
  private static async testElementKeyboardSupport(
    page: Page, 
    element: InteractiveElement
  ): Promise<KeyboardTestResult> {
    const result: KeyboardTestResult = {
      canNavigateWithTab: false,
      canActivateWithEnter: false,
      canActivateWithSpace: false,
      canEscapeWithEscape: false,
      supportsArrowKeys: false,
      hasKeyboardShortcuts: false,
      issues: []
    };
    
    try {
      // Test Tab navigation
      await page.focus(element.selector);
      const isFocused = await page.evaluate((selector) => {
        const el = document.querySelector(selector);
        return document.activeElement === el;
      }, element.selector);
      
      result.canNavigateWithTab = isFocused;
      
      if (!isFocused) {
        result.issues.push('Element cannot receive focus via Tab');
        return result;
      }
      
      // Test Enter key activation
      if (element.expectedKeys.includes('Enter')) {
        const enterWorks = await this.testKeyActivation(page, element.selector, 'Enter');
        result.canActivateWithEnter = enterWorks;
        if (!enterWorks) {
          result.issues.push('Element does not respond to Enter key');
        }
      }
      
      // Test Space key activation
      if (element.expectedKeys.includes('Space')) {
        const spaceWorks = await this.testKeyActivation(page, element.selector, 'Space');
        result.canActivateWithSpace = spaceWorks;
        if (!spaceWorks) {
          result.issues.push('Element does not respond to Space key');
        }
      }
      
      // Test Escape key (for modals, dropdowns)
      if (element.role === 'dialog' || element.role === 'menu') {
        const escapeWorks = await this.testKeyActivation(page, element.selector, 'Escape');
        result.canEscapeWithEscape = escapeWorks;
        if (!escapeWorks) {
          result.issues.push('Element does not respond to Escape key');
        }
      }
      
      // Test Arrow keys (for custom controls)
      if (element.expectedKeys.some(key => key.startsWith('Arrow'))) {
        const arrowWorks = await this.testArrowKeySupport(page, element.selector);
        result.supportsArrowKeys = arrowWorks;
        if (!arrowWorks) {
          result.issues.push('Element does not support arrow key navigation');
        }
      }
      
    } catch (error) {
      console.error(`Error testing keyboard support for ${element.selector}:`, error);
      result.issues.push('Error during keyboard testing');
    }
    
    return result;
  }

  /**
   * Test if a key activates an element
   */
  private static async testKeyActivation(page: Page, selector: string, key: string): Promise<boolean> {
    try {
      // Set up event listener to detect activation
      const activationDetected = await page.evaluate((sel, keyName) => {
        return new Promise<boolean>((resolve) => {
          const element = document.querySelector(sel);
          if (!element) {
            resolve(false);
            return;
          }
          
          let activated = false;
          const timeout = setTimeout(() => resolve(activated), 1000);
          
          // Listen for various activation events
          const events = ['click', 'change', 'submit', 'keydown'];
          const cleanup = () => {
            events.forEach(event => {
              element.removeEventListener(event, handler);
            });
            clearTimeout(timeout);
          };
          
          const handler = (event: Event) => {
            if (event.type === 'keydown') {
              const keyEvent = event as KeyboardEvent;
              if (keyEvent.key === keyName || keyEvent.code === keyName) {
                activated = true;
                cleanup();
                resolve(true);
              }
            } else {
              activated = true;
              cleanup();
              resolve(true);
            }
          };
          
          events.forEach(event => {
            element.addEventListener(event, handler);
          });
        });
      }, selector, key);
      
      // Focus element and press key
      await page.focus(selector);
      await page.keyboard.press(key as any);
      
      return activationDetected;
    } catch (error) {
      console.error(`Error testing ${key} activation:`, error);
      return false;
    }
  }

  /**
   * Test arrow key support for custom controls
   */
  private static async testArrowKeySupport(page: Page, selector: string): Promise<boolean> {
    try {
      await page.focus(selector);
      
      // Test each arrow key
      const arrowKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
      let anyArrowWorks = false;
      
      for (const key of arrowKeys) {
        const works = await this.testKeyActivation(page, selector, key);
        if (works) {
          anyArrowWorks = true;
          break;
        }
      }
      
      return anyArrowWorks;
    } catch (error) {
      console.error('Error testing arrow key support:', error);
      return false;
    }
  }

  /**
   * Detect keyboard shortcut conflicts
   */
  private static async detectShortcutConflicts(page: Page): Promise<string[]> {
    const conflicts: string[] = [];
    
    try {
      // Test common browser shortcuts that shouldn't be overridden
      const criticalShortcuts = [
        { keys: 'Ctrl+T', description: 'New tab' },
        { keys: 'Ctrl+W', description: 'Close tab' },
        { keys: 'Ctrl+R', description: 'Refresh' },
        { keys: 'Ctrl+L', description: 'Address bar' },
        { keys: 'F5', description: 'Refresh' },
        { keys: 'Alt+Tab', description: 'Switch applications' }
      ];
      
      for (const shortcut of criticalShortcuts) {
        const isOverridden = await page.evaluate((keys) => {
          return new Promise<boolean>((resolve) => {
            let prevented = false;
            const timeout = setTimeout(() => resolve(prevented), 500);
            
            const handler = (event: KeyboardEvent) => {
              if (event.defaultPrevented) {
                prevented = true;
                document.removeEventListener('keydown', handler);
                clearTimeout(timeout);
                resolve(true);
              }
            };
            
            document.addEventListener('keydown', handler);
            
            // Simulate the shortcut (simplified)
            const event = new KeyboardEvent('keydown', {
              key: keys.split('+').pop(),
              ctrlKey: keys.includes('Ctrl'),
              altKey: keys.includes('Alt'),
              shiftKey: keys.includes('Shift')
            });
            
            document.dispatchEvent(event);
          });
        }, shortcut.keys);
        
        if (isOverridden) {
          conflicts.push(`Critical shortcut ${shortcut.keys} (${shortcut.description}) is overridden`);
        }
      }
    } catch (error) {
      console.error('Error detecting shortcut conflicts:', error);
    }
    
    return conflicts;
  }

  /**
   * Generate comprehensive keyboard analysis report
   */
  static async generateKeyboardReport(page: Page): Promise<KeyboardAnalysisResult> {
    const navigationResult = await this.testKeyboardAccessibility(page);
    
    const accessibilityScore = navigationResult.totalElements > 0 
      ? (navigationResult.accessibleElements / navigationResult.totalElements) * 100 
      : 0;
    
    return {
      elementSelector: 'body', // Overall page analysis
      elementType: 'page',
      isReachable: navigationResult.accessibleElements > 0,
      isOperable: navigationResult.inaccessibleElements.length === 0,
      supportedKeys: ['Tab', 'Enter', 'Space', 'Escape'],
      hasKeyboardTrap: navigationResult.shortcutConflicts.length > 0,
      focusOrderPosition: 0,
      isLogicalOrder: navigationResult.navigationIssues.length === 0,
      issues: [
        ...navigationResult.inaccessibleElements,
        ...navigationResult.navigationIssues,
        ...navigationResult.shortcutConflicts
      ],
      recommendations: navigationResult.inaccessibleElements.length > 0
        ? ['Ensure all interactive elements are keyboard accessible']
        : []
    };
  }
}
