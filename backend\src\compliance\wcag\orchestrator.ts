import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import puppeteer from 'puppeteer';
import { v4 as uuidv4 } from 'uuid';
import { WcagDatabase } from './database/wcag-database';
import { WCAG_RULES, CATEGORY_WEIGHTS, VERSION_WEIGHTS, LEVEL_REQUIREMENTS } from './constants';
import { getCheckImplementation, getAutomationLevel } from './checks';

// Types
export interface WcagScanConfig {
  targetUrl: string;
  timeout?: number;
  wcagVersion?: '2.0' | '2.1' | '2.2' | '3.0';
  level?: 'A' | 'AA' | 'AAA';
  categories?: string[];
  rules?: string[];
  enableManualReview?: boolean;
  maxConcurrentChecks?: number;
}

export interface ScanProgress {
  scanId: string;
  status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
  currentRule?: string;
  completedRules: string[];
  totalRules: number;
  progress: number;
  estimatedTimeRemaining?: number;
  startTime: Date;
  lastUpdate: Date;
}

export interface WcagScanResult {
  scanId: string;
  targetUrl: string;
  config: WcagScanConfig;
  summary: WcagScanSummary;
  ruleResults: WcagRuleResult[];
  scanProgress: ScanProgress;
  createdAt: Date;
  completedAt?: Date;
}

export interface WcagScanSummary {
  overallScore: number;
  levelAchieved: 'A' | 'AA' | 'AAA' | 'None';
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
  totalRules: number;
  passedRules: number;
  failedRules: number;
  warningRules: number;
  categoryScores: Record<string, number>;
  versionScores: Record<string, number>;
  recommendations: string[];
}

export interface WcagRuleResult {
  ruleId: string;
  ruleName: string;
  category: string;
  version: string;
  level: string;
  status: 'passed' | 'failed' | 'warning' | 'not_applicable';
  score: number;
  maxScore: number;
  automationLevel: number;
  details: any;
  recommendations: string[];
  executionTime: number;
}

/**
 * WCAG Orchestrator - Part 7 Implementation
 * Coordinates comprehensive WCAG compliance scanning with all 21 rules
 */
export class WcagOrchestrator {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private database: WcagDatabase;
  private activeScanIds: Set<string> = new Set();
  private scanProgress: Map<string, ScanProgress> = new Map();

  constructor() {
    this.database = new WcagDatabase();
  }

  /**
   * Perform comprehensive WCAG scan
   */
  async performComprehensiveScan(config: WcagScanConfig): Promise<string> {
    const scanId = this.generateScanId();
    
    try {
      // Initialize scan progress
      await this.initializeScanProgress(scanId, config);
      
      // Launch browser and navigate to target
      await this.launchBrowser();
      await this.navigateToTarget(config.targetUrl);
      
      // Execute all checks
      const ruleResults = await this.executeAllChecks(scanId, config);
      
      // Calculate scan summary
      const summary = await this.calculateScanSummary(ruleResults, config);
      
      // Create final scan result
      const scanResult: WcagScanResult = {
        scanId,
        targetUrl: config.targetUrl,
        config,
        summary,
        ruleResults,
        scanProgress: this.scanProgress.get(scanId)!,
        createdAt: new Date(),
        completedAt: new Date()
      };
      
      // Store results in database
      await this.database.saveScanResult(scanResult);
      
      // Update progress to completed
      await this.updateScanProgress(scanId, 'completed', undefined, 100);
      
      console.log(`✅ Comprehensive WCAG scan completed: ${scanId}`);
      console.log(`📊 Overall Score: ${summary.overallScore}% | Level: ${summary.levelAchieved} | Risk: ${summary.riskLevel}`);
      
      return scanId;
      
    } catch (error) {
      console.error(`❌ Error in comprehensive scan ${scanId}:`, error);
      await this.updateScanProgress(scanId, 'failed');
      throw error;
    } finally {
      await this.cleanupBrowser();
      this.activeScanIds.delete(scanId);
    }
  }

  /**
   * Get scan progress
   */
  async getScanProgress(scanId: string): Promise<ScanProgress | null> {
    // First check in-memory cache
    const memoryProgress = this.scanProgress.get(scanId);
    if (memoryProgress) {
      return memoryProgress;
    }
    
    // Then check database
    return await this.database.getScanProgress(scanId);
  }

  /**
   * Cancel active scan
   */
  async cancelScan(scanId: string): Promise<boolean> {
    if (!this.activeScanIds.has(scanId)) {
      return false;
    }
    
    await this.updateScanProgress(scanId, 'cancelled');
    this.activeScanIds.delete(scanId);
    
    console.log(`🛑 Scan cancelled: ${scanId}`);
    return true;
  }

  /**
   * Initialize scan progress tracking
   */
  private async initializeScanProgress(scanId: string, config: WcagScanConfig): Promise<void> {
    const rulesToCheck = this.getRulesToCheck(config);
    
    const progress: ScanProgress = {
      scanId,
      status: 'initializing',
      completedRules: [],
      totalRules: rulesToCheck.length,
      progress: 0,
      startTime: new Date(),
      lastUpdate: new Date()
    };
    
    this.scanProgress.set(scanId, progress);
    this.activeScanIds.add(scanId);
    
    await this.database.storeScanProgress(progress);
    
    console.log(`🚀 Initialized scan ${scanId} with ${rulesToCheck.length} rules`);
  }

  /**
   * Update scan progress
   */
  private async updateScanProgress(
    scanId: string, 
    status?: ScanProgress['status'],
    currentRule?: string,
    progressOverride?: number
  ): Promise<void> {
    const progress = this.scanProgress.get(scanId);
    if (!progress) return;
    
    if (status) progress.status = status;
    if (currentRule) progress.currentRule = currentRule;
    
    // Calculate progress percentage
    if (progressOverride !== undefined) {
      progress.progress = progressOverride;
    } else {
      progress.progress = Math.round((progress.completedRules.length / progress.totalRules) * 100);
    }
    
    // Estimate time remaining
    if (progress.completedRules.length > 0 && progress.status === 'running') {
      const elapsed = Date.now() - progress.startTime.getTime();
      const avgTimePerRule = elapsed / progress.completedRules.length;
      const remainingRules = progress.totalRules - progress.completedRules.length;
      progress.estimatedTimeRemaining = Math.round((remainingRules * avgTimePerRule) / 1000);
    }
    
    progress.lastUpdate = new Date();
    
    await this.database.updateScanProgress(progress);
    
    console.log(`📈 Scan ${scanId}: ${progress.progress}% (${progress.completedRules.length}/${progress.totalRules})`);
  }

  /**
   * Launch browser instance
   */
  private async launchBrowser(): Promise<void> {
    if (this.browser) {
      await this.cleanupBrowser();
    }
    
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    
    this.page = await this.browser.newPage();
    
    // Set viewport and user agent
    await this.page.setViewport({ width: 1920, height: 1080 });
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    console.log('🌐 Browser launched successfully');
  }

  /**
   * Navigate to target URL
   */
  private async navigateToTarget(targetUrl: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser page not available');
    }

    console.log(`🔗 Navigating to: ${targetUrl}`);

    await this.page.goto(targetUrl, {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    // Wait for page to be fully loaded
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('✅ Navigation completed');
  }

  /**
   * Execute all WCAG checks
   */
  private async executeAllChecks(scanId: string, config: WcagScanConfig): Promise<WcagRuleResult[]> {
    const rulesToCheck = this.getRulesToCheck(config);
    const results: WcagRuleResult[] = [];

    await this.updateScanProgress(scanId, 'running');

    console.log(`🔍 Executing ${rulesToCheck.length} WCAG checks...`);

    for (const rule of rulesToCheck) {
      try {
        await this.updateScanProgress(scanId, 'running', rule.ruleId);

        const startTime = Date.now();
        const checkImplementation = getCheckImplementation(rule.ruleId);

        if (!checkImplementation) {
          console.warn(`⚠️ No implementation found for rule: ${rule.ruleId}`);
          continue;
        }

        // Create check configuration
        const checkConfig = {
          targetUrl: config.targetUrl,
          timeout: config.timeout || 30000,
          scanId,
          page: this.page!,
          enableManualReview: config.enableManualReview || false
        };

        // Execute the check
        console.log(`🔍 Executing rule: ${rule.ruleName} (${rule.ruleId})`);
        const checkResult = await checkImplementation.performCheck(checkConfig);

        const executionTime = Date.now() - startTime;

        // Create rule result
        const ruleResult: WcagRuleResult = {
          ruleId: rule.ruleId,
          ruleName: rule.ruleName,
          category: rule.category,
          version: rule.wcagVersion,
          level: rule.level,
          status: checkResult.status,
          score: checkResult.score,
          maxScore: checkResult.maxScore || 100,
          automationLevel: getAutomationLevel(rule.ruleId),
          details: checkResult.details || {},
          recommendations: checkResult.recommendations || [],
          executionTime
        };

        results.push(ruleResult);

        // Update progress
        const progress = this.scanProgress.get(scanId);
        if (progress) {
          progress.completedRules.push(rule.ruleId);
          await this.updateScanProgress(scanId);
        }

        console.log(`✅ Rule ${rule.ruleId} completed: ${checkResult.status} (${checkResult.score}/${ruleResult.maxScore})`);

      } catch (error) {
        console.error(`❌ Error executing rule ${rule.ruleId}:`, error);

        // Create failed result
        const failedResult: WcagRuleResult = {
          ruleId: rule.ruleId,
          ruleName: rule.ruleName,
          category: rule.category,
          version: rule.wcagVersion,
          level: rule.level,
          status: 'failed',
          score: 0,
          maxScore: 100,
          automationLevel: getAutomationLevel(rule.ruleId),
          details: { error: error instanceof Error ? error.message : 'Unknown error' },
          recommendations: ['Manual review required due to execution error'],
          executionTime: 0
        };

        results.push(failedResult);
      }
    }

    console.log(`✅ All checks completed: ${results.length} results`);
    return results;
  }

  /**
   * Calculate comprehensive scan summary
   */
  private async calculateScanSummary(results: WcagRuleResult[], config: WcagScanConfig): Promise<WcagScanSummary> {
    const totalRules = results.length;
    const passedRules = results.filter(r => r.status === 'passed').length;
    const failedRules = results.filter(r => r.status === 'failed').length;
    const warningRules = results.filter(r => r.status === 'warning').length;

    // Calculate overall score
    const overallScore = this.calculateOverallScore(results);

    // Determine WCAG level achieved
    const levelAchieved = this.determineLevelAchieved(results, config);

    // Determine risk level
    const riskLevel = this.determineRiskLevel(overallScore, failedRules, totalRules);

    // Calculate category scores
    const categoryScores = this.calculateCategoryScores(results);

    // Calculate version scores
    const versionScores = this.calculateVersionScores(results);

    // Generate recommendations
    const recommendations = this.generateRecommendations(results, overallScore, levelAchieved);

    return {
      overallScore,
      levelAchieved,
      riskLevel,
      totalRules,
      passedRules,
      failedRules,
      warningRules,
      categoryScores,
      versionScores,
      recommendations
    };
  }

  /**
   * Calculate overall weighted score
   */
  private calculateOverallScore(results: WcagRuleResult[]): number {
    if (results.length === 0) return 0;

    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const result of results) {
      const categoryWeight = CATEGORY_WEIGHTS[result.category] || 1;
      const versionWeight = VERSION_WEIGHTS[result.version] || 1;
      const weight = categoryWeight * versionWeight;

      totalWeightedScore += (result.score / result.maxScore) * 100 * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? Math.round(totalWeightedScore / totalWeight) : 0;
  }

  /**
   * Determine WCAG level achieved
   */
  private determineLevelAchieved(results: WcagRuleResult[], config: WcagScanConfig): 'A' | 'AA' | 'AAA' | 'None' {
    const targetLevel = config.level || 'AA';

    // Check each level in order
    for (const level of ['A', 'AA', 'AAA'] as const) {
      const levelThreshold = LEVEL_REQUIREMENTS[level];
      const levelResults = results.filter(r => {
        if (level === 'A') return r.level === 'A';
        if (level === 'AA') return r.level === 'A' || r.level === 'AA';
        return true; // AAA includes all levels
      });

      if (levelResults.length === 0) continue;

      const passedCount = levelResults.filter(r => r.status === 'passed').length;
      const passRate = passedCount / levelResults.length;

      // Use the threshold from LEVEL_REQUIREMENTS
      if (passRate < levelThreshold) {
        // Return previous level or None
        if (level === 'A') return 'None';
        if (level === 'AA') return 'A';
        if (level === 'AAA') return 'AA';
      }

      // If we've reached the target level, return it
      if (level === targetLevel) return level;
    }

    return targetLevel;
  }

  /**
   * Determine risk level based on score and failures
   */
  private determineRiskLevel(overallScore: number, failedRules: number, totalRules: number): 'Low' | 'Medium' | 'High' | 'Critical' {
    const failureRate = totalRules > 0 ? failedRules / totalRules : 0;

    if (overallScore >= 90 && failureRate <= 0.05) return 'Low';
    if (overallScore >= 75 && failureRate <= 0.15) return 'Medium';
    if (overallScore >= 50 && failureRate <= 0.30) return 'High';
    return 'Critical';
  }

  /**
   * Calculate category-specific scores
   */
  private calculateCategoryScores(results: WcagRuleResult[]): Record<string, number> {
    const categoryScores: Record<string, number> = {};
    const categoryGroups: Record<string, WcagRuleResult[]> = {};

    // Group results by category
    for (const result of results) {
      if (!categoryGroups[result.category]) {
        categoryGroups[result.category] = [];
      }
      categoryGroups[result.category].push(result);
    }

    // Calculate score for each category
    for (const [category, categoryResults] of Object.entries(categoryGroups)) {
      const totalScore = categoryResults.reduce((sum, r) => sum + r.score, 0);
      const maxScore = categoryResults.reduce((sum, r) => sum + r.maxScore, 0);
      categoryScores[category] = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
    }

    return categoryScores;
  }

  /**
   * Calculate version-specific scores
   */
  private calculateVersionScores(results: WcagRuleResult[]): Record<string, number> {
    const versionScores: Record<string, number> = {};
    const versionGroups: Record<string, WcagRuleResult[]> = {};

    // Group results by version
    for (const result of results) {
      if (!versionGroups[result.version]) {
        versionGroups[result.version] = [];
      }
      versionGroups[result.version].push(result);
    }

    // Calculate score for each version
    for (const [version, versionResults] of Object.entries(versionGroups)) {
      const totalScore = versionResults.reduce((sum, r) => sum + r.score, 0);
      const maxScore = versionResults.reduce((sum, r) => sum + r.maxScore, 0);
      versionScores[version] = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
    }

    return versionScores;
  }

  /**
   * Generate improvement recommendations
   */
  private generateRecommendations(results: WcagRuleResult[], overallScore: number, levelAchieved: string): string[] {
    const recommendations: string[] = [];

    // Overall score recommendations
    if (overallScore < 50) {
      recommendations.push('Critical accessibility issues detected. Immediate remediation required.');
    } else if (overallScore < 75) {
      recommendations.push('Significant accessibility improvements needed to meet compliance standards.');
    } else if (overallScore < 90) {
      recommendations.push('Good accessibility foundation with room for improvement.');
    }

    // Level-specific recommendations
    if (levelAchieved === 'None') {
      recommendations.push('Focus on basic Level A compliance requirements first.');
    } else if (levelAchieved === 'A') {
      recommendations.push('Work towards Level AA compliance for better accessibility.');
    } else if (levelAchieved === 'AA') {
      recommendations.push('Consider Level AAA enhancements for optimal accessibility.');
    }

    // Category-specific recommendations
    const failedResults = results.filter(r => r.status === 'failed');
    const categoryFailures: Record<string, number> = {};

    for (const result of failedResults) {
      categoryFailures[result.category] = (categoryFailures[result.category] || 0) + 1;
    }

    // Add category-specific recommendations
    for (const [category, count] of Object.entries(categoryFailures)) {
      if (count >= 3) {
        recommendations.push(`Address multiple issues in ${category} category (${count} failures).`);
      }
    }

    // Rule-specific recommendations
    for (const result of failedResults.slice(0, 5)) { // Top 5 failures
      if (result.recommendations.length > 0) {
        recommendations.push(`${result.ruleName}: ${result.recommendations[0]}`);
      }
    }

    return recommendations.slice(0, 10); // Limit to 10 recommendations
  }

  /**
   * Get rules to check based on configuration
   */
  private getRulesToCheck(config: WcagScanConfig): any[] {
    let rules = WCAG_RULES;

    // Filter by version
    if (config.wcagVersion) {
      rules = rules.filter(rule => rule.wcagVersion === config.wcagVersion);
    }

    // Filter by level
    if (config.level) {
      const allowedLevels = config.level === 'A' ? ['A'] :
                           config.level === 'AA' ? ['A', 'AA'] :
                           ['A', 'AA', 'AAA'];
      rules = rules.filter(rule => allowedLevels.includes(rule.level));
    }

    // Filter by categories
    if (config.categories && config.categories.length > 0) {
      rules = rules.filter(rule => config.categories!.includes(rule.category));
    }

    // Filter by specific rules
    if (config.rules && config.rules.length > 0) {
      rules = rules.filter(rule => config.rules!.includes(rule.ruleId));
    }

    return rules;
  }

  /**
   * Create empty scan summary
   */
  private createEmptySummary(): WcagScanSummary {
    return {
      overallScore: 0,
      levelAchieved: 'None',
      riskLevel: 'Critical',
      totalRules: 0,
      passedRules: 0,
      failedRules: 0,
      warningRules: 0,
      categoryScores: {},
      versionScores: {},
      recommendations: ['No scan data available']
    };
  }

  /**
   * Generate unique scan ID
   */
  private generateScanId(): string {
    return `wcag_${Date.now()}_${uuidv4().substring(0, 8)}`;
  }

  /**
   * Cleanup browser resources
   */
  private async cleanupBrowser(): Promise<void> {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    } catch (error) {
      console.error('Error during browser cleanup:', error);
    }
  }

  /**
   * Get total checks count for configuration
   */
  private getTotalChecksCount(config: WcagScanConfig): number {
    return this.getRulesToCheck(config).length;
  }
}
