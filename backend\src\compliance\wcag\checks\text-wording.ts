/**
 * WCAG Rule 18: Text and Wording - 3.1.5
 * 75% Automated - Manual review for context and domain-specific terminology
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';
const fleschKincaid = require('flesch-kincaid');
const { syllable } = require('syllable');
const natural = require('natural');
const compromise = require('compromise');
const Sentiment = require('sentiment');

interface TextAnalysis {
  text: string;
  wordCount: number;
  sentenceCount: number;
  syllableCount: number;
  fleschScore: number;
  fleschGrade: number;
  complexWords: string[];
  readabilityLevel: string;
  sentiment: any;
  entities: string[];
}

export class TextWordingCheck {
  private checkTemplate = new ManualReviewTemplate();
  private sentiment = new Sentiment();

  /**
   * Perform text and wording check - 75% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-018',
      'Text and Wording',
      'understandable',
      0.07,
      'AAA',
      0.75, // 75% automation rate
      config,
      this.executeTextWordingCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive text and wording analysis
   */
  private async executeTextWordingCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze main content text
    const contentAnalysis = await this.analyzeContentText(page);
    
    // Analyze form labels and instructions
    const formAnalysis = await this.analyzeFormText(page);
    
    // Analyze error messages
    const errorAnalysis = await this.analyzeErrorMessages(page);
    
    // Analyze navigation and UI text
    const uiAnalysis = await this.analyzeUIText(page);

    // Combine all analyses
    const allAnalyses = [contentAnalysis, formAnalysis, errorAnalysis, uiAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 75 // 75% automation as specified for Part 5
    };
  }

  /**
   * Analyze main content text for readability
   */
  private async analyzeContentText(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const contentText = await page.evaluate(() => {
        // Get main content areas
        const contentSelectors = [
          'main',
          '[role="main"]',
          'article',
          '.content',
          '#content',
          '.main-content',
          'body',
        ];

        let content = '';
        for (const selector of contentSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            // Remove script and style elements
            const clone = element.cloneNode(true) as Element;
            clone.querySelectorAll('script, style, nav, header, footer').forEach(el => el.remove());
            content = clone.textContent || '';
            break;
          }
        }

        return content.trim();
      });

      if (!contentText || contentText.length < 50) {
        evidence.push({
          type: 'warning',
          message: 'Insufficient content text for analysis',
          element: 'main content',
          details: { textLength: contentText.length },
        });

        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      const analysis = this.analyzeText(contentText);
      let totalChecks = 3;
      let passedChecks = 0;

      // Check readability level
      if (analysis.fleschScore >= 60) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: `Content readability is acceptable (Flesch score: ${analysis.fleschScore})`,
          element: 'main content',
          details: {
            fleschScore: analysis.fleschScore,
            readabilityLevel: analysis.readabilityLevel,
            gradeLevel: analysis.fleschGrade,
          },
        });
      } else {
        issues.push(`Content readability may be too difficult (Flesch score: ${analysis.fleschScore})`);
        recommendations.push('Consider simplifying language and sentence structure');
        
        evidence.push({
          type: 'warning',
          message: `Content may be difficult to read (Flesch score: ${analysis.fleschScore})`,
          element: 'main content',
          details: {
            fleschScore: analysis.fleschScore,
            readabilityLevel: analysis.readabilityLevel,
            gradeLevel: analysis.fleschGrade,
          },
        });
      }

      // Check for complex words
      if (analysis.complexWords.length <= analysis.wordCount * 0.1) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: `Complex word usage is reasonable (${analysis.complexWords.length}/${analysis.wordCount})`,
          element: 'main content',
          details: {
            complexWordsCount: analysis.complexWords.length,
            totalWords: analysis.wordCount,
            percentage: Math.round((analysis.complexWords.length / analysis.wordCount) * 100),
          },
        });
      } else {
        issues.push(`High complex word usage: ${analysis.complexWords.length}/${analysis.wordCount}`);
        recommendations.push('Consider using simpler alternatives for complex words');
        
        evidence.push({
          type: 'warning',
          message: `High complex word usage detected`,
          element: 'main content',
          details: {
            complexWordsCount: analysis.complexWords.length,
            totalWords: analysis.wordCount,
            sampleComplexWords: analysis.complexWords.slice(0, 10),
          },
        });
      }

      // Check sentence length
      const avgSentenceLength = analysis.wordCount / analysis.sentenceCount;
      if (avgSentenceLength <= 20) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: `Average sentence length is appropriate (${avgSentenceLength.toFixed(1)} words)`,
          element: 'main content',
          details: {
            averageSentenceLength: avgSentenceLength,
            sentenceCount: analysis.sentenceCount,
          },
        });
      } else {
        issues.push(`Long average sentence length: ${avgSentenceLength.toFixed(1)} words`);
        recommendations.push('Consider breaking long sentences into shorter ones');
        
        evidence.push({
          type: 'warning',
          message: `Sentences may be too long on average`,
          element: 'main content',
          details: {
            averageSentenceLength: avgSentenceLength,
            sentenceCount: analysis.sentenceCount,
          },
        });
      }

      // Add manual review for domain-specific content
      if (analysis.entities.length > 0 || analysis.complexWords.length > 5) {
        manualReviewItems.push({
          type: 'domain_terminology',
          description: 'Review domain-specific terminology and technical language for clarity',
          element: 'main content',
          priority: 'medium',
          estimatedTime: 8,
          context: {
            textAnalysis: analysis,
            complexWordsCount: analysis.complexWords.length,
            entitiesCount: analysis.entities.length,
          },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing content text',
        element: 'main content',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze content text'],
        recommendations: ['Check content readability manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze form labels and instructions
   */
  private async analyzeFormText(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const formTexts = await page.$$eval('form', (forms) => {
        return forms.map((form, index) => {
          const labels = Array.from(form.querySelectorAll('label')).map(label => label.textContent?.trim() || '');
          const instructions = Array.from(form.querySelectorAll('.instruction, .help-text, [role="note"]'))
            .map(el => el.textContent?.trim() || '');
          const placeholders = Array.from(form.querySelectorAll('input[placeholder], textarea[placeholder]'))
            .map(el => el.getAttribute('placeholder') || '');

          return {
            index,
            labels: labels.filter(text => text.length > 0),
            instructions: instructions.filter(text => text.length > 0),
            placeholders: placeholders.filter(text => text.length > 0),
          };
        });
      });

      let totalChecks = formTexts.length;
      let passedChecks = 0;

      formTexts.forEach((form, index) => {
        const allTexts = [...form.labels, ...form.instructions, ...form.placeholders];
        
        if (allTexts.length === 0) {
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Form ${index + 1} has no text to analyze`,
            element: `form:nth-of-type(${index + 1})`,
            details: { hasText: false },
          });
          return;
        }

        const combinedText = allTexts.join(' ');
        const analysis = this.analyzeText(combinedText);

        if (analysis.fleschScore >= 70 || combinedText.length < 100) {
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Form ${index + 1} text is clear and understandable`,
            element: `form:nth-of-type(${index + 1})`,
            details: {
              fleschScore: analysis.fleschScore,
              textLength: combinedText.length,
            },
          });
        } else {
          issues.push(`Form ${index + 1} text may be difficult to understand`);
          evidence.push({
            type: 'warning',
            message: `Form ${index + 1} text may need simplification`,
            element: `form:nth-of-type(${index + 1})`,
            details: {
              fleschScore: analysis.fleschScore,
              complexWords: analysis.complexWords.slice(0, 5),
            },
          });

          manualReviewItems.push({
            type: 'form_text_clarity',
            description: `Review form ${index + 1} text for clarity and simplicity`,
            element: `form:nth-of-type(${index + 1})`,
            priority: 'high',
            estimatedTime: 4,
            context: {
              formIndex: index + 1,
              textAnalysis: analysis,
              textsToReview: allTexts,
            },
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing form text',
        element: 'form',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze form text'],
        recommendations: ['Check form text manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze error messages for clarity
   */
  private async analyzeErrorMessages(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const errorMessages = await page.$$eval('.error, .alert-danger, [role="alert"], .validation-error', (elements) => {
        return elements.map((el, index) => ({
          index,
          text: el.textContent?.trim() || '',
          isVisible: el.offsetWidth > 0 && el.offsetHeight > 0,
        })).filter(msg => msg.text.length > 0);
      });

      let totalChecks = errorMessages.length || 1;
      let passedChecks = 0;

      if (errorMessages.length === 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: 'No error messages found to analyze',
          element: 'page',
          details: { errorMessagesFound: false },
        });
      } else {
        errorMessages.forEach((error, index) => {
          const analysis = this.analyzeText(error.text);
          
          if (analysis.fleschScore >= 80 || error.text.length < 50) {
            passedChecks++;
            evidence.push({
              type: 'info',
              message: `Error message ${index + 1} is clear and understandable`,
              element: '.error, .alert-danger, [role="alert"]',
              details: {
                message: error.text,
                fleschScore: analysis.fleschScore,
              },
            });
          } else {
            issues.push(`Error message ${index + 1} may be unclear`);
            evidence.push({
              type: 'warning',
              message: `Error message ${index + 1} may need simplification`,
              element: '.error, .alert-danger, [role="alert"]',
              details: {
                message: error.text,
                fleschScore: analysis.fleschScore,
              },
            });
          }
        });

        // Add manual review for error message effectiveness
        manualReviewItems.push({
          type: 'error_message_clarity',
          description: 'Review error messages for clarity and actionable guidance',
          element: 'error messages',
          priority: 'high',
          estimatedTime: 3,
          context: {
            errorMessagesCount: errorMessages.length,
            errorMessages: errorMessages.map(msg => msg.text),
          },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing error messages',
        element: 'error messages',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze error messages'],
        recommendations: ['Check error messages manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze UI text (buttons, links, navigation)
   */
  private async analyzeUIText(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const uiTexts = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button')).map(btn => btn.textContent?.trim() || '');
        const links = Array.from(document.querySelectorAll('a')).map(link => link.textContent?.trim() || '');
        const navItems = Array.from(document.querySelectorAll('nav a, .nav a, .menu a')).map(item => item.textContent?.trim() || '');

        return {
          buttons: buttons.filter(text => text.length > 0),
          links: links.filter(text => text.length > 0),
          navItems: navItems.filter(text => text.length > 0),
        };
      });

      let totalChecks = 1;
      let passedChecks = 0;

      const allUITexts = [...uiTexts.buttons, ...uiTexts.links, ...uiTexts.navItems];
      
      // Check for vague or unclear UI text
      const vagueTerms = ['click here', 'read more', 'learn more', 'here', 'more', 'link', 'button'];
      const vagueTexts = allUITexts.filter(text => 
        vagueTerms.some(term => text.toLowerCase().includes(term))
      );

      if (vagueTexts.length === 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: 'UI text appears descriptive and clear',
          element: 'buttons, links, navigation',
          details: {
            totalUITexts: allUITexts.length,
            vagueTextsFound: 0,
          },
        });
      } else {
        issues.push(`Found ${vagueTexts.length} potentially vague UI text elements`);
        recommendations.push('Use more descriptive text for buttons and links');
        
        evidence.push({
          type: 'warning',
          message: `Found potentially vague UI text`,
          element: 'buttons, links, navigation',
          details: {
            vagueTextsCount: vagueTexts.length,
            examples: vagueTexts.slice(0, 5),
          },
        });

        manualReviewItems.push({
          type: 'ui_text_clarity',
          description: 'Review UI text for descriptiveness and clarity',
          element: 'buttons, links, navigation',
          priority: 'medium',
          estimatedTime: 5,
          context: {
            vagueTextsCount: vagueTexts.length,
            vagueTexts: vagueTexts,
            totalUITexts: allUITexts.length,
          },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing UI text',
        element: 'UI elements',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze UI text'],
        recommendations: ['Check UI text manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze text using NLP and readability metrics
   */
  private analyzeText(text: string): TextAnalysis {
    const sentences = natural.SentenceTokenizer.tokenize(text);
    const words = natural.WordTokenizer.tokenize(text.toLowerCase());
    const syllableCount = words.reduce((total, word) => total + syllable(word), 0);
    
    const fleschScore = fleschKincaid({
      sentence: sentences.length,
      word: words.length,
      syllable: syllableCount,
    });

    // Determine reading level
    let readabilityLevel = 'Very Difficult';
    if (fleschScore >= 90) readabilityLevel = 'Very Easy';
    else if (fleschScore >= 80) readabilityLevel = 'Easy';
    else if (fleschScore >= 70) readabilityLevel = 'Fairly Easy';
    else if (fleschScore >= 60) readabilityLevel = 'Standard';
    else if (fleschScore >= 50) readabilityLevel = 'Fairly Difficult';
    else if (fleschScore >= 30) readabilityLevel = 'Difficult';

    // Find complex words (3+ syllables)
    const complexWords = words.filter(word => syllable(word) >= 3);

    // Calculate grade level
    const fleschGrade = 0.39 * (words.length / sentences.length) + 11.8 * (syllableCount / words.length) - 15.59;

    // Analyze sentiment
    const sentimentResult = this.sentiment.analyze(text);

    // Extract entities using compromise
    const doc = compromise(text);
    const entities = doc.people().out('array').concat(doc.places().out('array'));

    return {
      text,
      wordCount: words.length,
      sentenceCount: sentences.length,
      syllableCount,
      fleschScore: Math.round(fleschScore * 10) / 10,
      fleschGrade: Math.round(fleschGrade * 10) / 10,
      complexWords: [...new Set(complexWords)], // Remove duplicates
      readabilityLevel,
      sentiment: sentimentResult,
      entities: [...new Set(entities)], // Remove duplicates
    };
  }
}
