/**
 * WCAG Rule 14: Target Size - 2.5.8
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { WcagEvidence } from '../types';

export class TargetSizeCheck {
  private checkTemplate = new CheckTemplate();

  /**
   * Perform target size check - 100% automated
   */
  async performCheck(config: CheckConfig) {
    return this.checkTemplate.executeCheck(
      'WCAG-014',
      'Target Size',
      'operable',
      0.08,
      'AA',
      config,
      this.executeTargetSizeCheck.bind(this),
      true, // Requires browser
      false // No manual review
    );
  }

  /**
   * Execute target size analysis
   */
  private async executeTargetSizeCheck(page: Page, config: CheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Get all interactive elements (targets)
    const interactiveElements = await page.evaluate(() => {
      const elements: Array<{
        selector: string;
        tagName: string;
        width: number;
        height: number;
        x: number;
        y: number;
        isVisible: boolean;
        hasClickHandler: boolean;
      }> = [];

      // Interactive element selectors
      const interactiveSelectors = [
        'button',
        'a[href]',
        'input[type="button"]',
        'input[type="submit"]',
        'input[type="reset"]',
        'input[type="checkbox"]',
        'input[type="radio"]',
        'select',
        'textarea',
        '[onclick]',
        '[role="button"]',
        '[role="link"]',
        '[role="menuitem"]',
        '[role="tab"]',
        '[tabindex]:not([tabindex="-1"])'
      ];

      function generateSelector(element: HTMLElement, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }
        
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }
        
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      interactiveSelectors.forEach(selector => {
        const foundElements = document.querySelectorAll(selector);
        foundElements.forEach((element, index) => {
          const htmlElement = element as HTMLElement;
          const rect = htmlElement.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(htmlElement);

          const isVisible = computedStyle.display !== 'none' &&
                           computedStyle.visibility !== 'hidden' &&
                           computedStyle.opacity !== '0' &&
                           rect.width > 0 &&
                           rect.height > 0;

          if (isVisible) {
            elements.push({
              selector: generateSelector(htmlElement, index),
              tagName: htmlElement.tagName.toLowerCase(),
              width: rect.width,
              height: rect.height,
              x: rect.left,
              y: rect.top,
              isVisible,
              hasClickHandler: htmlElement.onclick !== null || 
                              htmlElement.getAttribute('onclick') !== null
            });
          }
        });
      });

      return elements;
    });

    let totalElements = 0;
    let passedElements = 0;
    const minimumSize = 44; // WCAG 2.5.8 minimum target size

    // Analyze each interactive element
    for (const element of interactiveElements) {
      totalElements++;

      const meetsMinimumSize = element.width >= minimumSize && element.height >= minimumSize;
      const hasAdequateSpacing = await this.checkTargetSpacing(page, element);

      if (meetsMinimumSize || hasAdequateSpacing.hasSpacing) {
        passedElements++;

        evidence.push({
          type: 'measurement',
          description: 'Target meets size requirements',
          value: `Size: ${Math.round(element.width)}x${Math.round(element.height)}px ${hasAdequateSpacing.hasSpacing ? '(adequate spacing)' : ''}`,
          selector: element.selector,
          severity: 'info'
        });
      } else {
        issues.push(`Target ${element.selector} is too small: ${Math.round(element.width)}x${Math.round(element.height)}px`);

        evidence.push({
          type: 'measurement',
          description: 'Target fails size requirements',
          value: `Size: ${Math.round(element.width)}x${Math.round(element.height)}px (minimum: ${minimumSize}x${minimumSize}px)`,
          selector: element.selector,
          severity: 'error'
        });

        recommendations.push(`Increase ${element.selector} size to at least ${minimumSize}x${minimumSize}px or provide adequate spacing`);
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Target size analysis summary',
      value: `${passedElements}/${totalElements} interactive targets meet size requirements`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error'
    });

    if (score < 100) {
      recommendations.unshift('Ensure all interactive targets are at least 44x44px or have adequate spacing');
      recommendations.push('Consider increasing padding or margin for small targets');
      recommendations.push('Test target accessibility on touch devices');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations
    };
  }

  /**
   * Check if target has adequate spacing from other targets
   */
  private async checkTargetSpacing(page: Page, target: any): Promise<{
    hasSpacing: boolean;
    spacingDetails: string;
  }> {
    return await page.evaluate((targetData) => {
      const minimumSpacing = 8; // Minimum spacing between targets
      const targetRect = {
        left: targetData.x,
        top: targetData.y,
        right: targetData.x + targetData.width,
        bottom: targetData.y + targetData.height
      };

      // Find all other interactive elements
      const otherTargets = document.querySelectorAll('button, a[href], input[type="button"], input[type="submit"], [onclick], [role="button"]');
      
      let hasAdequateSpacing = true;
      const conflicts: string[] = [];

      otherTargets.forEach(otherElement => {
        const otherRect = (otherElement as HTMLElement).getBoundingClientRect();
        
        // Skip if it's the same element
        if (otherRect.left === targetData.x && otherRect.top === targetData.y) {
          return;
        }

        // Calculate distance between targets
        const horizontalDistance = Math.max(0, 
          Math.max(targetRect.left - otherRect.right, otherRect.left - targetRect.right)
        );
        const verticalDistance = Math.max(0,
          Math.max(targetRect.top - otherRect.bottom, otherRect.top - targetRect.bottom)
        );

        const totalDistance = Math.sqrt(horizontalDistance * horizontalDistance + verticalDistance * verticalDistance);

        if (totalDistance < minimumSpacing) {
          hasAdequateSpacing = false;
          conflicts.push(`${otherElement.tagName.toLowerCase()} at distance ${Math.round(totalDistance)}px`);
        }
      });

      return {
        hasSpacing: hasAdequateSpacing,
        spacingDetails: conflicts.length > 0 
          ? `Conflicts with: ${conflicts.join(', ')}`
          : 'Adequate spacing from other targets'
      };
    }, target);
  }
}
