/**
 * Foundation Tests
 * Verify basic WCAG module setup
 */

import { WCAG_AUTOMATED_RULES, WCAG_MANUAL_RULES, AUTOMATED_CATEGORY_WEIGHTS, AUTOMATED_VERSION_WEIGHTS } from '../constants';
import { WcagRuleConfig } from '../types';

describe('WCAG Foundation', () => {
  test('should have automated rules defined', () => {
    expect(WCAG_AUTOMATED_RULES.length).toBeGreaterThan(0);

    // Verify each automated rule has required properties
    WCAG_AUTOMATED_RULES.forEach((rule: WcagRuleConfig) => {
      expect(rule.ruleId).toBeDefined();
      expect(rule.ruleName).toBeDefined();
      expect(rule.category).toBeDefined();
      expect(rule.wcagVersion).toBeDefined();
      expect(rule.level).toBeDefined();
      expect(rule.weight).toBeGreaterThan(0);
      expect(rule.automated).toBeDefined();
      expect(rule.checkFunction).toBeDefined();
      expect(rule.automated).toBe(true); // Must be automated
    });
  });

  test('should have manual rules defined separately', () => {
    expect(WCAG_MANUAL_RULES.length).toBeGreaterThan(0);

    // Verify manual rules don't have scoring weights
    WCAG_MANUAL_RULES.forEach(rule => {
      expect((rule as any).weight).toBeUndefined();
      expect(rule.priority).toBeDefined();
      expect(rule.estimatedTime).toBeDefined();
    });
  });

  test('should have proper weight distribution', () => {
    const totalWeight = WCAG_AUTOMATED_RULES.reduce((sum, rule) => sum + rule.weight, 0);
    expect(totalWeight).toBeCloseTo(1.0, 2);
  });

  test('should have valid category weights', () => {
    const totalCategoryWeight = Object.values(AUTOMATED_CATEGORY_WEIGHTS).reduce((sum, weight) => sum + weight, 0);
    expect(totalCategoryWeight).toBe(1.0);
  });

  test('should have valid version weights', () => {
    const totalVersionWeight = Object.values(AUTOMATED_VERSION_WEIGHTS).reduce((sum, weight) => sum + weight, 0);
    expect(totalVersionWeight).toBe(1.0);
  });

  test('should have no any[] types in type definitions', () => {
    // This test ensures TypeScript compilation catches any[] usage
    expect(true).toBe(true); // Will fail compilation if any[] types exist
  });

  test('should have 21 automated rules total', () => {
    expect(WCAG_AUTOMATED_RULES.length).toBe(21);
  });

  test('should have rules distributed across WCAG versions', () => {
    const wcag21Rules = WCAG_AUTOMATED_RULES.filter(rule => rule.wcagVersion === '2.1');
    const wcag22Rules = WCAG_AUTOMATED_RULES.filter(rule => rule.wcagVersion === '2.2');
    const wcag30Rules = WCAG_AUTOMATED_RULES.filter(rule => rule.wcagVersion === '3.0');

    expect(wcag21Rules.length).toBeGreaterThan(0);
    expect(wcag22Rules.length).toBeGreaterThan(0);
    expect(wcag30Rules.length).toBeGreaterThan(0);
  });

  test('should have rules distributed across categories', () => {
    const perceivableRules = WCAG_AUTOMATED_RULES.filter(rule => rule.category === 'perceivable');
    const operableRules = WCAG_AUTOMATED_RULES.filter(rule => rule.category === 'operable');
    const understandableRules = WCAG_AUTOMATED_RULES.filter(rule => rule.category === 'understandable');
    const robustRules = WCAG_AUTOMATED_RULES.filter(rule => rule.category === 'robust');

    expect(perceivableRules.length).toBeGreaterThan(0);
    expect(operableRules.length).toBeGreaterThan(0);
    expect(understandableRules.length).toBeGreaterThan(0);
    expect(robustRules.length).toBeGreaterThan(0);
  });

  test('should have rules distributed across levels', () => {
    const levelARules = WCAG_AUTOMATED_RULES.filter(rule => rule.level === 'A');
    const levelAARules = WCAG_AUTOMATED_RULES.filter(rule => rule.level === 'AA');
    const levelAAARules = WCAG_AUTOMATED_RULES.filter(rule => rule.level === 'AAA');

    expect(levelARules.length).toBeGreaterThan(0);
    expect(levelAARules.length).toBeGreaterThan(0);
    expect(levelAAARules.length).toBeGreaterThan(0);
  });

  test('should have unique rule IDs', () => {
    const ruleIds = WCAG_AUTOMATED_RULES.map(rule => rule.ruleId);
    const uniqueRuleIds = new Set(ruleIds);
    expect(uniqueRuleIds.size).toBe(ruleIds.length);
  });

  test('should have valid success criteria format', () => {
    WCAG_AUTOMATED_RULES.forEach(rule => {
      // WCAG 2.x should have format like "1.1.1", "2.4.7"
      // WCAG 3.0 should have format like "2.1", "2.2"
      if (rule.wcagVersion === '3.0') {
        expect(rule.successCriterion).toMatch(/^\d+\.\d+$/);
      } else {
        expect(rule.successCriterion).toMatch(/^\d+\.\d+\.\d+$/);
      }
    });
  });
});
