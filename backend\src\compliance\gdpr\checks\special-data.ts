import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface SpecialDataCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class SpecialDataCheck {
  /**
   * Check for special category data processing
   * REAL ANALYSIS - scans for sensitive data indicators
   */
  async performCheck(config: SpecialDataCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Analyze for special category data indicators
      const specialDataAnalysis = await this.analyzeSpecialCategoryData(page);

      if (specialDataAnalysis.indicators.length === 0) {
        score = 100; // No special data indicators found
        evidence.push({
          type: 'text',
          description: 'No special category data indicators found',
          value: 'No sensitive data processing detected',
        });
      } else {
        // Check for explicit consent mechanisms for special data
        const consentAnalysis = await this.analyzeExplicitConsent(
          page,
          specialDataAnalysis.indicators,
        );

        const indicatorsWithConsent = consentAnalysis.filter((c) => c.hasExplicitConsent).length;
        const totalIndicators = specialDataAnalysis.indicators.length;

        score =
          totalIndicators > 0 ? Math.round((indicatorsWithConsent / totalIndicators) * 100) : 100;

        // Add evidence for found indicators
        for (const indicator of specialDataAnalysis.indicators) {
          evidence.push({
            type: 'text',
            description: `Special category data indicator: ${indicator.category}`,
            value: `Keywords: ${indicator.keywords.join(', ')}`,
            location: indicator.location,
          });
        }

        // Add evidence for consent analysis
        for (const consent of consentAnalysis) {
          if (consent.hasExplicitConsent) {
            evidence.push({
              type: 'text',
              description: `Explicit consent found for ${consent.category}`,
              value: consent.consentEvidence,
            });
          } else {
            evidence.push({
              type: 'text',
              description: `Missing explicit consent for ${consent.category}`,
              value: 'Explicit consent required for special category data',
            });
          }
        }
      }

      // Always requires manual review due to legal complexity
      return {
        ruleId: 'GDPR-013',
        ruleName: 'Special Category Data',
        category: 'data_protection',
        passed: score >= 80, // Higher threshold for special data
        score,
        weight: 4,
        severity: 'high',
        evidence,
        recommendations: this.generateRecommendations(specialDataAnalysis, score),
        manualReviewRequired: true, // Always requires legal review
      };
    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze page for special category data indicators
   */
  private async analyzeSpecialCategoryData(page: Page): Promise<{
    indicators: Array<{
      category: string;
      keywords: string[];
      location: string;
    }>;
  }> {
    return await page.evaluate(() => {
      const specialCategories = [
        {
          category: 'Health Data',
          keywords: [
            'health',
            'medical',
            'healthcare',
            'diagnosis',
            'treatment',
            'medication',
            'patient',
            'doctor',
            'hospital',
            'clinic',
          ],
        },
        {
          category: 'Biometric Data',
          keywords: [
            'biometric',
            'fingerprint',
            'facial recognition',
            'iris scan',
            'voice recognition',
            'dna',
            'genetic',
          ],
        },
        {
          category: 'Racial/Ethnic Origin',
          keywords: ['race', 'ethnicity', 'ethnic origin', 'racial', 'nationality', 'heritage'],
        },
        {
          category: 'Political Opinions',
          keywords: [
            'political',
            'politics',
            'voting',
            'election',
            'party affiliation',
            'political views',
          ],
        },
        {
          category: 'Religious Beliefs',
          keywords: [
            'religion',
            'religious',
            'faith',
            'belief',
            'spiritual',
            'church',
            'mosque',
            'temple',
          ],
        },
        {
          category: 'Trade Union Membership',
          keywords: ['trade union', 'union membership', 'labor union', 'worker union'],
        },
        {
          category: 'Sexual Orientation',
          keywords: [
            'sexual orientation',
            'sexuality',
            'sexual preference',
            'lgbtq',
            'gay',
            'lesbian',
          ],
        },
        {
          category: 'Criminal Convictions',
          keywords: [
            'criminal',
            'conviction',
            'criminal record',
            'offense',
            'crime',
            'legal proceedings',
          ],
        },
      ];

      const pageText = document.body.textContent?.toLowerCase() || '';
      const indicators: Array<{ category: string; keywords: string[]; location: string }> = [];

      for (const category of specialCategories) {
        const foundKeywords = category.keywords.filter((keyword) =>
          pageText.includes(keyword.toLowerCase()),
        );

        if (foundKeywords.length > 0) {
          // Try to find the specific location
          let location = 'page content';
          const elements = document.querySelectorAll('*');

          for (const element of elements) {
            const elementText = element.textContent?.toLowerCase() || '';
            if (foundKeywords.some((keyword) => elementText.includes(keyword))) {
              const parent = element.closest(
                'section, div[class*="privacy"], div[class*="terms"], form',
              );
              if (parent) {
                location = parent.tagName.toLowerCase();
                break;
              }
            }
          }

          indicators.push({
            category: category.category,
            keywords: foundKeywords,
            location,
          });
        }
      }

      return { indicators };
    });
  }

  /**
   * Analyze explicit consent mechanisms for special data
   */
  private async analyzeExplicitConsent(
    page: Page,
    indicators: Array<{ category: string; keywords: string[] }>,
  ): Promise<
    Array<{
      category: string;
      hasExplicitConsent: boolean;
      consentEvidence: string;
    }>
  > {
    return await page.evaluate((indicatorData) => {
      const pageText = document.body.textContent?.toLowerCase() || '';
      const explicitConsentKeywords = [
        'explicit consent',
        'expressly consent',
        'specifically consent',
        'clear consent',
        'unambiguous consent',
        'informed consent',
      ];

      return indicatorData.map((indicator) => {
        // Look for explicit consent language related to this category
        const categoryKeywords = indicator.keywords.map((k) => k.toLowerCase());
        let hasExplicitConsent = false;
        let consentEvidence = '';

        // Check if explicit consent language appears near category keywords
        for (const consentKeyword of explicitConsentKeywords) {
          if (pageText.includes(consentKeyword)) {
            // Check if it's in proximity to category keywords
            const consentIndex = pageText.indexOf(consentKeyword);
            const surroundingText = pageText.substring(
              Math.max(0, consentIndex - 200),
              consentIndex + 200,
            );

            if (categoryKeywords.some((catKeyword) => surroundingText.includes(catKeyword))) {
              hasExplicitConsent = true;
              consentEvidence = `Found "${consentKeyword}" near ${indicator.category.toLowerCase()} content`;
              break;
            }
          }
        }

        return {
          category: indicator.category,
          hasExplicitConsent,
          consentEvidence: consentEvidence || 'No explicit consent language found',
        };
      });
    }, indicators);
  }

  /**
   * Generate recommendations for special data compliance
   */
  private generateRecommendations(
    analysis: { indicators: any[] },
    score: number,
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (analysis.indicators.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Legal review required for special category data',
        description: `${analysis.indicators.length} special data categories detected requiring legal assessment`,
        implementation:
          'Have legal expert review special category data processing and consent mechanisms',
        effort: 'significant',
        impact: 'high',
      });

      if (score < 80) {
        recommendations.push({
          priority: 2,
          title: 'Implement explicit consent for special data',
          description:
            'Add clear, explicit consent mechanisms for all special category data processing',
          implementation: 'Update consent forms and privacy notices with explicit consent language',
          effort: 'moderate',
          impact: 'high',
        });
      }
    }

    recommendations.push({
      priority: 3,
      title: 'Document legal basis for special data processing',
      description: 'Ensure proper legal basis documentation for any special category data',
      implementation: 'Create documentation showing legal basis under GDPR Article 9',
      effort: 'moderate',
      impact: 'high',
    });

    return recommendations;
  }

  /**
   * Create error result
   */
  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-013',
      ruleName: 'Special Category Data',
      category: 'data_protection',
      passed: false,
      score: 0,
      weight: 4,
      severity: 'high',
      evidence: [
        {
          type: 'text',
          description: 'Special data check failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        },
      ],
      recommendations: [
        {
          priority: 1,
          title: 'Manual legal review required',
          description: 'Special category data requires expert legal assessment',
          implementation: 'Consult legal expert for GDPR Article 9 compliance',
          effort: 'significant',
          impact: 'high',
        },
      ],
      manualReviewRequired: true,
    };
  }
}
