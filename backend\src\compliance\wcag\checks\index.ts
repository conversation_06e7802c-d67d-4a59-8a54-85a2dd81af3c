/**
 * WCAG Checks Index
 * Exports all automated WCAG checks
 */

// Part 3: Fully Automated Checks (100% automation)
export { ContrastMinimumCheck } from './contrast-minimum';
export { FocusVisibleCheck } from './focus-visible';
export { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
export { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
export { FocusAppearanceCheck } from './focus-appearance';
export { TargetSizeCheck } from './target-size';

// Part 4: Very High Automation Checks (85-95% automation)
export { NonTextContentCheck } from './non-text-content';
export { InfoRelationshipsCheck } from './info-relationships';
export { KeyboardCheck } from './keyboard';
export { ErrorIdentificationCheck } from './error-identification';
export { NameRoleValueCheck } from './name-role-value';
export { RedundantEntryCheck } from './redundant-entry';
export { ImageAlternatives3Check } from './image-alternatives-3';
export { KeyboardFocus3Check } from './keyboard-focus-3';

// Part 5: High & Medium Automation Checks (60-80% automation)
export { CaptionsCheck } from './captions';
export { FocusOrderCheck } from './focus-order';
export { DraggingMovementsCheck } from './dragging-movements';
export { ConsistentHelpCheck } from './consistent-help';
export { TextWordingCheck } from './text-wording';
export { MotorCheck } from './motor';
export { PronunciationMeaningCheck } from './pronunciation-meaning';

// Check registry for easy access
export const AUTOMATED_CHECKS = {
  // Part 3: Fully Automated (100%)
  'WCAG-004': 'ContrastMinimumCheck',
  'WCAG-007': 'FocusVisibleCheck',
  'WCAG-010': 'FocusNotObscuredMinimumCheck',
  'WCAG-011': 'FocusNotObscuredEnhancedCheck',
  'WCAG-012': 'FocusAppearanceCheck',
  'WCAG-014': 'TargetSizeCheck',

  // Part 4: Very High Automation (85-95%)
  'WCAG-001': 'NonTextContentCheck',
  'WCAG-003': 'InfoRelationshipsCheck',
  'WCAG-005': 'KeyboardCheck',
  'WCAG-008': 'ErrorIdentificationCheck',
  'WCAG-009': 'NameRoleValueCheck',
  'WCAG-016': 'RedundantEntryCheck',
  'WCAG-017': 'ImageAlternatives3Check',
  'WCAG-019': 'KeyboardFocus3Check',

  // Part 5: High & Medium Automation (60-80%)
  'WCAG-002': 'CaptionsCheck',
  'WCAG-006': 'FocusOrderCheck',
  'WCAG-013': 'DraggingMovementsCheck',
  'WCAG-015': 'ConsistentHelpCheck',
  'WCAG-018': 'TextWordingCheck',
  'WCAG-020': 'MotorCheck',
  'WCAG-021': 'PronunciationMeaningCheck'
} as const;

// Check registry mapping for Part 7 orchestrator
export const WCAG_CHECK_REGISTRY = {
  // Part 3: Fully Automated (100%)
  'WCAG-004': ContrastMinimumCheck,
  'WCAG-007': FocusVisibleCheck,
  'WCAG-010': FocusNotObscuredMinimumCheck,
  'WCAG-011': FocusNotObscuredEnhancedCheck,
  'WCAG-012': FocusAppearanceCheck,
  'WCAG-014': TargetSizeCheck,

  // Part 4: Very High Automation (85-95%)
  'WCAG-001': NonTextContentCheck,
  'WCAG-003': InfoRelationshipsCheck,
  'WCAG-005': KeyboardCheck,
  'WCAG-008': ErrorIdentificationCheck,
  'WCAG-009': NameRoleValueCheck,
  'WCAG-016': RedundantEntryCheck,
  'WCAG-017': ImageAlternatives3Check,
  'WCAG-019': KeyboardFocus3Check,

  // Part 5: High & Medium Automation (60-80%)
  'WCAG-002': CaptionsCheck,
  'WCAG-006': FocusOrderCheck,
  'WCAG-013': DraggingMovementsCheck,
  'WCAG-015': ConsistentHelpCheck,
  'WCAG-018': TextWordingCheck,
  'WCAG-020': MotorCheck,
  'WCAG-021': PronunciationMeaningCheck
} as const;

/**
 * Get check implementation by rule ID
 */
export function getCheckImplementation(ruleId: string) {
  return WCAG_CHECK_REGISTRY[ruleId as keyof typeof WCAG_CHECK_REGISTRY];
}

/**
 * Get automation level for a rule
 */
export function getAutomationLevel(ruleId: string): number {
  const automationLevels: Record<string, number> = {
    'WCAG-001': 0.95, 'WCAG-002': 0.80, 'WCAG-003': 0.90, 'WCAG-004': 1.00,
    'WCAG-005': 0.85, 'WCAG-006': 0.75, 'WCAG-007': 1.00, 'WCAG-008': 0.90,
    'WCAG-009': 0.90, 'WCAG-010': 1.00, 'WCAG-011': 1.00, 'WCAG-012': 1.00,
    'WCAG-013': 0.70, 'WCAG-014': 1.00, 'WCAG-015': 0.80, 'WCAG-016': 0.85,
    'WCAG-017': 0.95, 'WCAG-018': 0.75, 'WCAG-019': 0.90, 'WCAG-020': 0.80,
    'WCAG-021': 0.60
  };
  return automationLevels[ruleId] || 0;
}

export type AutomatedCheckType = keyof typeof AUTOMATED_CHECKS;
