/**
 * WCAG Rule 15: Consistent Help - 3.2.6
 * 80% Automated - Manual review for help content quality and multi-page consistency
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

interface HelpMechanism {
  type: 'contact' | 'phone' | 'email' | 'chat' | 'form' | 'faq' | 'help-text' | 'tutorial';
  element: string;
  text: string;
  position: { x: number; y: number };
  isVisible: boolean;
}

export class ConsistentHelpCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform consistent help check - 80% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-015',
      'Consistent Help',
      'understandable',
      0.05,
      'AA',
      0.80, // 80% automation rate
      config,
      this.executeConsistentHelpCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive consistent help analysis
   */
  private async executeConsistentHelpCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze help mechanisms
    const helpMechanisms = await this.analyzeHelpMechanisms(page);
    
    // Analyze contact information
    const contactAnalysis = await this.analyzeContactInformation(page);
    
    // Analyze help documentation
    const helpDocAnalysis = await this.analyzeHelpDocumentation(page);
    
    // Analyze form help
    const formHelpAnalysis = await this.analyzeFormHelp(page);

    // Combine all analyses
    const allAnalyses = [helpMechanisms, contactAnalysis, helpDocAnalysis, formHelpAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 80 // 80% automation as specified for Part 5
    };
  }

  /**
   * Analyze help mechanisms on the page
   */
  private async analyzeHelpMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const helpElements = await page.evaluate(() => {
        const helpSelectors = [
          'a[href*="help"]',
          'a[href*="support"]',
          'a[href*="contact"]',
          'a[href*="faq"]',
          '.help',
          '.support',
          '.contact',
          '.faq',
          '[aria-label*="help"]',
          '[title*="help"]',
          'button[data-help]',
          '.help-button',
          '.support-button',
        ];

        const mechanisms: HelpMechanism[] = [];

        helpSelectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const text = element.textContent?.trim() || element.getAttribute('aria-label') || element.getAttribute('title') || '';
            
            if (text && rect.width > 0 && rect.height > 0) {
              let type: HelpMechanism['type'] = 'help-text';
              
              if (text.toLowerCase().includes('contact') || selector.includes('contact')) type = 'contact';
              else if (text.toLowerCase().includes('phone') || text.match(/\d{3}-\d{3}-\d{4}/)) type = 'phone';
              else if (text.toLowerCase().includes('email') || text.includes('@')) type = 'email';
              else if (text.toLowerCase().includes('chat')) type = 'chat';
              else if (text.toLowerCase().includes('form')) type = 'form';
              else if (text.toLowerCase().includes('faq')) type = 'faq';
              else if (text.toLowerCase().includes('tutorial')) type = 'tutorial';

              mechanisms.push({
                type,
                element: selector,
                text,
                position: { x: rect.left, y: rect.top },
                isVisible: true,
              });
            }
          });
        });

        return mechanisms;
      });

      let totalChecks = 1;
      let passedChecks = 0;

      if (helpElements.length > 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: `Found ${helpElements.length} help mechanism(s)`,
          element: 'help elements',
          details: {
            helpMechanismsCount: helpElements.length,
            types: [...new Set(helpElements.map(h => h.type))],
          },
        });

        // Group by type for analysis
        const helpByType = helpElements.reduce((acc, help) => {
          if (!acc[help.type]) acc[help.type] = [];
          acc[help.type].push(help);
          return acc;
        }, {} as Record<string, HelpMechanism[]>);

        // Add manual review for consistency across pages
        manualReviewItems.push({
          type: 'help_consistency',
          description: 'Verify help mechanisms are consistently placed across multiple pages',
          element: 'help mechanisms',
          priority: 'high',
          estimatedTime: 10,
          context: {
            helpMechanismsFound: helpElements.length,
            helpTypes: Object.keys(helpByType),
            helpByType,
          },
        });

      } else {
        evidence.push({
          type: 'warning',
          message: 'No help mechanisms found on this page',
          element: 'page',
          details: { helpMechanismsFound: false },
        });

        recommendations.push('Consider adding help mechanisms for user assistance');
        
        manualReviewItems.push({
          type: 'help_necessity',
          description: 'Evaluate if help mechanisms are needed for this page',
          element: 'page',
          priority: 'medium',
          estimatedTime: 3,
          context: {
            pageComplexity: 'unknown', // To be determined manually
          },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing help mechanisms',
        element: 'help elements',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze help mechanisms'],
        recommendations: ['Check help mechanisms manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze contact information consistency
   */
  private async analyzeContactInformation(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const contactInfo = await page.evaluate(() => {
        const phoneRegex = /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
        
        const bodyText = document.body.textContent || '';
        const phones = bodyText.match(phoneRegex) || [];
        const emails = bodyText.match(emailRegex) || [];

        // Look for contact links
        const contactLinks = Array.from(document.querySelectorAll('a[href*="contact"], a[href*="mailto:"], a[href*="tel:"]'))
          .map(link => ({
            href: link.getAttribute('href') || '',
            text: link.textContent?.trim() || '',
            type: link.getAttribute('href')?.startsWith('mailto:') ? 'email' : 
                  link.getAttribute('href')?.startsWith('tel:') ? 'phone' : 'contact',
          }));

        return {
          phones: [...new Set(phones)],
          emails: [...new Set(emails)],
          contactLinks,
        };
      });

      let totalChecks = 1;
      let passedChecks = 0;

      const hasContactInfo = contactInfo.phones.length > 0 || 
                            contactInfo.emails.length > 0 || 
                            contactInfo.contactLinks.length > 0;

      if (hasContactInfo) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: 'Contact information found on page',
          element: 'contact information',
          details: {
            phonesCount: contactInfo.phones.length,
            emailsCount: contactInfo.emails.length,
            contactLinksCount: contactInfo.contactLinks.length,
          },
        });

        // Add manual review for contact info consistency
        manualReviewItems.push({
          type: 'contact_consistency',
          description: 'Verify contact information is consistent across all pages',
          element: 'contact information',
          priority: 'medium',
          estimatedTime: 5,
          context: {
            contactInfo,
            hasMultipleContactMethods: (contactInfo.phones.length > 0 ? 1 : 0) + 
                                      (contactInfo.emails.length > 0 ? 1 : 0) + 
                                      (contactInfo.contactLinks.length > 0 ? 1 : 0) > 1,
          },
        });
      } else {
        evidence.push({
          type: 'info',
          message: 'No contact information found on this page',
          element: 'page',
          details: { contactInfoFound: false },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing contact information',
        element: 'contact information',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze contact information'],
        recommendations: ['Check contact information manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze help documentation
   */
  private async analyzeHelpDocumentation(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const helpDocs = await page.$$eval('a[href*="help"], a[href*="documentation"], a[href*="guide"], a[href*="tutorial"]', (links) => {
        return links.map((link, index) => ({
          index,
          href: link.getAttribute('href') || '',
          text: link.textContent?.trim() || '',
          isExternal: link.getAttribute('href')?.startsWith('http') && 
                     !link.getAttribute('href')?.includes(window.location.hostname),
        }));
      });

      let totalChecks = 1;
      let passedChecks = 0;

      if (helpDocs.length > 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          message: `Found ${helpDocs.length} help documentation link(s)`,
          element: 'help documentation',
          details: {
            helpDocsCount: helpDocs.length,
            externalLinks: helpDocs.filter(doc => doc.isExternal).length,
          },
        });

        // Add manual review for documentation quality
        manualReviewItems.push({
          type: 'help_documentation_quality',
          description: 'Review help documentation for completeness and accessibility',
          element: 'help documentation',
          priority: 'medium',
          estimatedTime: 8,
          context: {
            helpDocsCount: helpDocs.length,
            helpDocs: helpDocs.map(doc => ({ text: doc.text, href: doc.href })),
          },
        });
      } else {
        evidence.push({
          type: 'info',
          message: 'No help documentation links found',
          element: 'page',
          details: { helpDocsFound: false },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing help documentation',
        element: 'help documentation',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze help documentation'],
        recommendations: ['Check help documentation manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze form help and instructions
   */
  private async analyzeFormHelp(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const formHelp = await page.$$eval('form', (forms) => {
        return forms.map((form, index) => {
          const helpElements = form.querySelectorAll('.help-text, .instruction, [role="note"], .hint');
          const requiredFields = form.querySelectorAll('[required], [aria-required="true"]');
          const errorElements = form.querySelectorAll('.error, .invalid, [aria-invalid="true"]');

          return {
            index,
            hasHelpText: helpElements.length > 0,
            helpElementsCount: helpElements.length,
            requiredFieldsCount: requiredFields.length,
            errorElementsCount: errorElements.length,
            helpTexts: Array.from(helpElements).map(el => el.textContent?.trim() || ''),
          };
        });
      });

      let totalChecks = formHelp.length;
      let passedChecks = 0;

      if (formHelp.length === 0) {
        evidence.push({
          type: 'info',
          message: 'No forms found to analyze',
          element: 'page',
          details: { formsFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      formHelp.forEach((form, index) => {
        if (form.requiredFieldsCount === 0 || form.hasHelpText) {
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Form ${index + 1} has appropriate help text or no required fields`,
            element: `form:nth-of-type(${index + 1})`,
            details: {
              hasHelpText: form.hasHelpText,
              helpElementsCount: form.helpElementsCount,
              requiredFieldsCount: form.requiredFieldsCount,
            },
          });
        } else {
          issues.push(`Form ${index + 1} has required fields but lacks help text`);
          evidence.push({
            type: 'warning',
            message: `Form ${index + 1} may need help text for required fields`,
            element: `form:nth-of-type(${index + 1})`,
            details: {
              hasHelpText: form.hasHelpText,
              requiredFieldsCount: form.requiredFieldsCount,
            },
          });
          recommendations.push(`Add help text to form ${index + 1} for required fields`);
        }

        // Add manual review for form help consistency
        if (form.requiredFieldsCount > 0) {
          manualReviewItems.push({
            type: 'form_help_consistency',
            description: `Review form ${index + 1} help text for consistency and clarity`,
            element: `form:nth-of-type(${index + 1})`,
            priority: 'medium',
            estimatedTime: 4,
            context: {
              formIndex: index + 1,
              requiredFieldsCount: form.requiredFieldsCount,
              helpElementsCount: form.helpElementsCount,
              helpTexts: form.helpTexts,
            },
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing form help',
        element: 'form',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze form help'],
        recommendations: ['Check form help manually'],
        manualReviewItems,
      };
    }
  }
}
