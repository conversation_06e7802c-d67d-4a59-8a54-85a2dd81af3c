/**
 * WCAG Rule 20: Motor - 2.5.8
 * 80% Automated - Manual review for complex gesture alternatives
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export class MotorCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform motor accessibility check - 80% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-020',
      'Motor',
      'operable',
      0.04,
      'AA',
      0.80, // 80% automation rate
      config,
      this.executeMotorCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive motor accessibility analysis
   */
  private async executeMotorCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze target sizes
    const targetSizeAnalysis = await this.analyzeTargetSizes(page);
    
    // Analyze gesture requirements
    const gestureAnalysis = await this.analyzeGestureRequirements(page);
    
    // Analyze motion-based controls
    const motionAnalysis = await this.analyzeMotionControls(page);
    
    // Analyze timeout settings
    const timeoutAnalysis = await this.analyzeTimeouts(page);

    // Combine all analyses
    const allAnalyses = [targetSizeAnalysis, gestureAnalysis, motionAnalysis, timeoutAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 80 // 80% automation as specified for Part 5
    };
  }

  /**
   * Analyze target sizes for motor accessibility
   */
  private async analyzeTargetSizes(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const interactiveElements = await page.$$eval('button, a, input, select, textarea, [role="button"], [tabindex]:not([tabindex="-1"])', (elements) => {
        return elements.map((element, index) => {
          const rect = element.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(element);
          
          return {
            index,
            tagName: element.tagName.toLowerCase(),
            width: rect.width,
            height: rect.height,
            area: rect.width * rect.height,
            isVisible: rect.width > 0 && rect.height > 0 && computedStyle.visibility !== 'hidden',
            hasText: (element.textContent?.trim().length || 0) > 0,
            type: (element as HTMLInputElement).type || undefined,
          };
        }).filter(el => el.isVisible);
      });

      let totalChecks = interactiveElements.length;
      let passedChecks = 0;

      const minTargetSize = 44; // 44px minimum for AA compliance

      interactiveElements.forEach((element, index) => {
        const meetsMinSize = element.width >= minTargetSize && element.height >= minTargetSize;
        
        if (meetsMinSize) {
          passedChecks++;
          evidence.push({
            type: 'measurement',
            message: `Interactive element ${index + 1} meets minimum target size`,
            element: element.tagName,
            details: {
              width: element.width,
              height: element.height,
              meetsMinSize: true,
            },
          });
        } else {
          issues.push(`Interactive element ${index + 1} below minimum target size (${element.width}x${element.height}px)`);
          evidence.push({
            type: 'measurement',
            message: `Interactive element ${index + 1} may be too small for motor accessibility`,
            element: element.tagName,
            details: {
              width: element.width,
              height: element.height,
              minRequired: minTargetSize,
              meetsMinSize: false,
            },
          });
          recommendations.push(`Increase size of ${element.tagName} element ${index + 1} to at least ${minTargetSize}x${minTargetSize}px`);
        }
      });

      // Add manual review for spacing and layout
      if (interactiveElements.length > 5) {
        manualReviewItems.push({

          description: 'Review spacing between interactive elements for motor accessibility',
          element: 'interactive elements',
          priority: 'medium',
          estimatedTime: 5,
          context: {
            interactiveElementsCount: interactiveElements.length,
            smallTargetsCount: interactiveElements.filter(el => el.width < minTargetSize || el.height < minTargetSize).length,
          },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'measurement',
        message: 'Error analyzing target sizes',
        element: 'interactive elements',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze target sizes'],
        recommendations: ['Check target sizes manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze gesture requirements
   */
  private async analyzeGestureRequirements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const gestureElements = await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        const gestureElements: any[] = [];

        elements.forEach((element, index) => {
          const hasComplexGesture = element.hasAttribute('ontouchstart') || 
                                   element.hasAttribute('ontouchmove') || 
                                   element.className.includes('swipe') ||
                                   element.className.includes('pinch') ||
                                   element.className.includes('rotate');

          const hasSimpleAlternative = element.hasAttribute('onclick') || 
                                      element.hasAttribute('onmousedown') ||
                                      element.tagName.toLowerCase() === 'button' ||
                                      element.tagName.toLowerCase() === 'a';

          if (hasComplexGesture) {
            gestureElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              className: element.className,
              hasComplexGesture,
              hasSimpleAlternative,
              gestureType: element.className.includes('swipe') ? 'swipe' :
                          element.className.includes('pinch') ? 'pinch' :
                          element.className.includes('rotate') ? 'rotate' : 'touch',
            });
          }
        });

        return gestureElements;
      });

      let totalChecks = gestureElements.length;
      let passedChecks = 0;

      if (gestureElements.length === 0) {
        evidence.push({
          type: 'interaction',
          message: 'No complex gesture requirements detected',
          element: 'page',
          details: { complexGesturesFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      gestureElements.forEach((element, index) => {
        if (element.hasSimpleAlternative) {
          passedChecks++;
          evidence.push({
            type: 'interaction',
            message: `Gesture element ${index + 1} has simple alternative`,
            element: element.tagName,
            details: {
              gestureType: element.gestureType,
              hasSimpleAlternative: true,
            },
          });
        } else {
          issues.push(`Gesture element ${index + 1} lacks simple alternative`);
          evidence.push({
            type: 'interaction',
            message: `Gesture element ${index + 1} may need simple alternative`,
            element: element.tagName,
            details: {
              gestureType: element.gestureType,
              hasSimpleAlternative: false,
            },
          });
          recommendations.push(`Add simple click/tap alternative for ${element.gestureType} gesture`);
        }

        // Add manual review for all gesture elements
        manualReviewItems.push({

          description: `Test gesture element ${index + 1} for simple alternative functionality`,
          element: element.tagName,
          priority: 'high',
          estimatedTime: 3,
          context: {
            elementIndex: index + 1,
            gestureType: element.gestureType,
            hasSimpleAlternative: element.hasSimpleAlternative,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        message: 'Error analyzing gesture requirements',
        element: 'gesture elements',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze gesture requirements'],
        recommendations: ['Check gesture requirements manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze motion-based controls
   */
  private async analyzeMotionControls(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const motionElements = await page.evaluate(() => {
        // Look for elements that might use device motion
        const motionIndicators = [
          'shake',
          'tilt',
          'motion',
          'accelerometer',
          'gyroscope',
          'devicemotion',
          'deviceorientation',
        ];

        const elements = document.querySelectorAll('*');
        const motionElements: any[] = [];

        elements.forEach((element, index) => {
          const text = element.textContent?.toLowerCase() || '';
          const className = element.className.toLowerCase();
          const hasMotionIndicator = motionIndicators.some(indicator => 
            text.includes(indicator) || className.includes(indicator)
          );

          const hasMotionEvent = element.hasAttribute('ondevicemotion') || 
                                 element.hasAttribute('ondeviceorientation');

          if (hasMotionIndicator || hasMotionEvent) {
            motionElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              hasMotionIndicator,
              hasMotionEvent,
              text: text.substring(0, 100),
            });
          }
        });

        return motionElements;
      });

      let totalChecks = motionElements.length;
      let passedChecks = 0;

      if (motionElements.length === 0) {
        evidence.push({
          type: 'interaction',
          message: 'No motion-based controls detected',
          element: 'page',
          details: { motionControlsFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      motionElements.forEach((element, index) => {
        // All motion controls require manual review
        manualReviewItems.push({

          description: `Verify motion control ${index + 1} has non-motion alternative`,
          element: element.tagName,
          priority: 'high',
          estimatedTime: 4,
          context: {
            elementIndex: index + 1,
            hasMotionEvent: element.hasMotionEvent,
            text: element.text,
          },
        });

        evidence.push({
          type: 'interaction',
          message: `Motion control ${index + 1} requires manual verification`,
          element: element.tagName,
          details: {
            hasMotionEvent: element.hasMotionEvent,
            requiresManualCheck: true,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        message: 'Error analyzing motion controls',
        element: 'motion elements',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze motion controls'],
        recommendations: ['Check motion controls manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze timeout settings for motor accessibility
   */
  private async analyzeTimeouts(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const timeoutElements = await page.evaluate(() => {
        // Look for timeout-related elements
        const timeoutIndicators = [
          'timeout',
          'session',
          'expire',
          'countdown',
          'timer',
          'time limit',
        ];

        const elements = document.querySelectorAll('*');
        const timeoutElements: any[] = [];

        elements.forEach((element, index) => {
          const text = element.textContent?.toLowerCase() || '';
          const hasTimeoutIndicator = timeoutIndicators.some(indicator => 
            text.includes(indicator)
          );

          if (hasTimeoutIndicator) {
            timeoutElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              text: text.substring(0, 100),
              hasExtendOption: text.includes('extend') || text.includes('more time'),
            });
          }
        });

        // Check for meta refresh
        const metaRefresh = document.querySelector('meta[http-equiv="refresh"]');
        if (metaRefresh) {
          timeoutElements.push({
            index: -1,
            tagName: 'meta',
            text: 'Meta refresh detected',
            hasExtendOption: false,
          });
        }

        return timeoutElements;
      });

      let totalChecks = timeoutElements.length;
      let passedChecks = 0;

      if (timeoutElements.length === 0) {
        evidence.push({
          type: 'interaction',
          message: 'No timeout indicators found',
          element: 'page',
          details: { timeoutsFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      timeoutElements.forEach((element, index) => {
        if (element.hasExtendOption) {
          passedChecks++;
          evidence.push({
            type: 'interaction',
            message: `Timeout element ${index + 1} appears to have extend option`,
            element: element.tagName,
            details: {
              hasExtendOption: true,
              text: element.text,
            },
          });
        } else {
          evidence.push({
            type: 'interaction',
            message: `Timeout element ${index + 1} may need extend option`,
            element: element.tagName,
            details: {
              hasExtendOption: false,
              text: element.text,
            },
          });
        }

        // Add manual review for all timeout elements
        manualReviewItems.push({

          description: `Verify timeout ${index + 1} allows sufficient time or extension for motor disabilities`,
          element: element.tagName,
          priority: 'high',
          estimatedTime: 3,
          context: {
            elementIndex: index + 1,
            hasExtendOption: element.hasExtendOption,
            text: element.text,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        message: 'Error analyzing timeouts',
        element: 'timeout elements',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze timeouts'],
        recommendations: ['Check timeout settings manually'],
        manualReviewItems,
      };
    }
  }
}
