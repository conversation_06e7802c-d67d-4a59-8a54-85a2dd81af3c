/**
 * Layout Analysis Utilities
 * Provides visual layout analysis and positioning validation
 */

import { Page } from 'puppeteer';

export interface ElementLayout {
  selector: string;
  tagName: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isVisible: boolean;
  isInteractive: boolean;
  zIndex: number;
  position: string;
  overflow: string;
}

export interface OverlapResult {
  hasOverlaps: boolean;
  overlappingPairs: Array<{
    element1: string;
    element2: string;
    overlapArea: number;
    severity: 'minor' | 'moderate' | 'severe';
  }>;
  hiddenElements: string[];
}

export interface TargetSizeResult {
  element: string;
  width: number;
  height: number;
  meetsMinimum: boolean;
  recommendedSize: { width: number; height: number };
  touchFriendly: boolean;
}

export interface LayoutAnalysisResult {
  totalElements: number;
  visibleElements: number;
  overlappingElements: number;
  undersizedTargets: number;
  layoutIssues: string[];
  responsiveIssues: string[];
  zIndexConflicts: string[];
}

export class LayoutAnalyzer {
  // WCAG minimum target size (44x44 CSS pixels)
  private static readonly MIN_TARGET_SIZE = 44;
  private static readonly RECOMMENDED_TARGET_SIZE = 48;

  /**
   * Analyze overall page layout for accessibility issues
   */
  static async analyzePageLayout(page: Page): Promise<LayoutAnalysisResult> {
    const elements = await this.getAllLayoutElements(page);
    const overlapResult = await this.detectOverlaps(elements);
    const targetSizes = await this.analyzeTargetSizes(page);
    const responsiveIssues = await this.checkResponsiveLayout(page);
    const zIndexConflicts = await this.detectZIndexConflicts(elements);

    const layoutIssues: string[] = [];
    
    // Compile layout issues
    if (overlapResult.hasOverlaps) {
      layoutIssues.push(`${overlapResult.overlappingPairs.length} overlapping element pairs detected`);
    }
    
    if (overlapResult.hiddenElements.length > 0) {
      layoutIssues.push(`${overlapResult.hiddenElements.length} elements are hidden by overlaps`);
    }
    
    const undersizedTargets = targetSizes.filter(target => !target.meetsMinimum);
    if (undersizedTargets.length > 0) {
      layoutIssues.push(`${undersizedTargets.length} interactive elements below minimum target size`);
    }

    return {
      totalElements: elements.length,
      visibleElements: elements.filter(el => el.isVisible).length,
      overlappingElements: overlapResult.overlappingPairs.length * 2,
      undersizedTargets: undersizedTargets.length,
      layoutIssues,
      responsiveIssues,
      zIndexConflicts
    };
  }

  /**
   * Get layout information for all elements
   */
  private static async getAllLayoutElements(page: Page): Promise<ElementLayout[]> {
    return await page.evaluate(() => {
      const elements: ElementLayout[] = [];
      const allElements = document.querySelectorAll('*');
      
      allElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(element);
        
        // Only include elements with meaningful size
        if (rect.width > 0 || rect.height > 0) {
          const isInteractive = [
            'button', 'a', 'input', 'select', 'textarea'
          ].includes(element.tagName.toLowerCase()) ||
          element.hasAttribute('onclick') ||
          element.hasAttribute('role') ||
          parseInt(element.getAttribute('tabindex') || '-1') >= 0;

          elements.push({
            selector: element.tagName.toLowerCase() + (element.id ? `#${element.id}` : '') + 
                     (element.className ? `.${element.className.split(' ')[0]}` : '') + 
                     `:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            boundingBox: {
              x: rect.x,
              y: rect.y,
              width: rect.width,
              height: rect.height
            },
            isVisible: rect.width > 0 && rect.height > 0 && 
                      computedStyle.visibility !== 'hidden' && 
                      computedStyle.display !== 'none',
            isInteractive,
            zIndex: parseInt(computedStyle.zIndex) || 0,
            position: computedStyle.position,
            overflow: computedStyle.overflow
          });
        }
      });
      
      return elements;
    });
  }

  /**
   * Detect overlapping elements
   */
  private static async detectOverlaps(elements: ElementLayout[]): Promise<OverlapResult> {
    const overlappingPairs: OverlapResult['overlappingPairs'] = [];
    const hiddenElements: string[] = [];
    
    for (let i = 0; i < elements.length; i++) {
      for (let j = i + 1; j < elements.length; j++) {
        const el1 = elements[i];
        const el2 = elements[j];
        
        if (!el1.isVisible || !el2.isVisible) continue;
        
        const overlap = this.calculateOverlap(el1.boundingBox, el2.boundingBox);
        
        if (overlap > 0) {
          const el1Area = el1.boundingBox.width * el1.boundingBox.height;
          const el2Area = el2.boundingBox.width * el2.boundingBox.height;
          const overlapPercentage = overlap / Math.min(el1Area, el2Area);
          
          let severity: 'minor' | 'moderate' | 'severe';
          if (overlapPercentage > 0.8) {
            severity = 'severe';
            // Check if one element is completely hidden
            if (overlapPercentage > 0.95) {
              const hiddenElement = el1Area < el2Area ? el1.selector : el2.selector;
              if (!hiddenElements.includes(hiddenElement)) {
                hiddenElements.push(hiddenElement);
              }
            }
          } else if (overlapPercentage > 0.3) {
            severity = 'moderate';
          } else {
            severity = 'minor';
          }
          
          overlappingPairs.push({
            element1: el1.selector,
            element2: el2.selector,
            overlapArea: overlap,
            severity
          });
        }
      }
    }
    
    return {
      hasOverlaps: overlappingPairs.length > 0,
      overlappingPairs,
      hiddenElements
    };
  }

  /**
   * Calculate overlap area between two rectangles
   */
  private static calculateOverlap(
    rect1: { x: number; y: number; width: number; height: number },
    rect2: { x: number; y: number; width: number; height: number }
  ): number {
    const left = Math.max(rect1.x, rect2.x);
    const right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
    const top = Math.max(rect1.y, rect2.y);
    const bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);
    
    if (left < right && top < bottom) {
      return (right - left) * (bottom - top);
    }
    
    return 0;
  }

  /**
   * Analyze target sizes for interactive elements
   */
  static async analyzeTargetSizes(page: Page): Promise<TargetSizeResult[]> {
    return await page.evaluate((minSize, recSize) => {
      const results: TargetSizeResult[] = [];
      
      const interactiveSelectors = [
        'button', 'a[href]', 'input:not([type="hidden"])', 
        'select', 'textarea', '[role="button"]', '[onclick]'
      ];
      
      interactiveSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          const rect = element.getBoundingClientRect();
          
          if (rect.width > 0 && rect.height > 0) {
            const meetsMinimum = rect.width >= minSize && rect.height >= minSize;
            const touchFriendly = rect.width >= recSize && rect.height >= recSize;
            
            results.push({
              element: selector,
              width: rect.width,
              height: rect.height,
              meetsMinimum,
              recommendedSize: {
                width: Math.max(rect.width, recSize),
                height: Math.max(rect.height, recSize)
              },
              touchFriendly
            });
          }
        });
      });
      
      return results;
    }, this.MIN_TARGET_SIZE, this.RECOMMENDED_TARGET_SIZE);
  }

  /**
   * Check responsive layout issues
   */
  private static async checkResponsiveLayout(page: Page): Promise<string[]> {
    const issues: string[] = [];
    
    try {
      // Test different viewport sizes
      const viewports = [
        { width: 320, height: 568, name: 'Mobile Portrait' },
        { width: 768, height: 1024, name: 'Tablet Portrait' },
        { width: 1024, height: 768, name: 'Tablet Landscape' },
        { width: 1920, height: 1080, name: 'Desktop' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewport(viewport);
        
        // Check for horizontal scrolling
        const hasHorizontalScroll = await page.evaluate(() => {
          return document.documentElement.scrollWidth > document.documentElement.clientWidth;
        });
        
        if (hasHorizontalScroll && viewport.width <= 768) {
          issues.push(`Horizontal scrolling detected on ${viewport.name} (${viewport.width}px)`);
        }
        
        // Check for content overflow
        const overflowElements = await page.evaluate(() => {
          const elements = document.querySelectorAll('*');
          const overflowing: string[] = [];
          
          elements.forEach((element, index) => {
            const rect = element.getBoundingClientRect();
            if (rect.right > window.innerWidth + 10) { // 10px tolerance
              overflowing.push(`${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`);
            }
          });
          
          return overflowing.slice(0, 5); // Limit to first 5
        });
        
        if (overflowElements.length > 0) {
          issues.push(`Content overflow on ${viewport.name}: ${overflowElements.join(', ')}`);
        }
      }
      
      // Reset to default viewport
      await page.setViewport({ width: 1920, height: 1080 });
      
    } catch (error) {
      console.error('Error checking responsive layout:', error);
      issues.push('Error during responsive layout analysis');
    }
    
    return issues;
  }

  /**
   * Detect z-index conflicts that might affect accessibility
   */
  private static async detectZIndexConflicts(elements: ElementLayout[]): Promise<string[]> {
    const conflicts: string[] = [];
    
    // Group elements by z-index
    const zIndexGroups = new Map<number, ElementLayout[]>();
    
    elements.forEach(element => {
      if (element.position !== 'static' && element.zIndex !== 0) {
        const group = zIndexGroups.get(element.zIndex) || [];
        group.push(element);
        zIndexGroups.set(element.zIndex, group);
      }
    });
    
    // Check for potential conflicts
    zIndexGroups.forEach((group, zIndex) => {
      if (group.length > 1) {
        // Multiple elements with same z-index might conflict
        const interactiveElements = group.filter(el => el.isInteractive);
        if (interactiveElements.length > 1) {
          conflicts.push(`Multiple interactive elements share z-index ${zIndex}`);
        }
      }
    });
    
    // Check for extremely high z-index values (potential accessibility barriers)
    const highZIndexElements = elements.filter(el => el.zIndex > 9999);
    if (highZIndexElements.length > 0) {
      conflicts.push(`${highZIndexElements.length} elements with extremely high z-index values`);
    }
    
    return conflicts;
  }

  /**
   * Check if an element is obscured by other elements
   */
  static async checkElementObscured(page: Page, selector: string): Promise<{
    isObscured: boolean;
    obscuringElements: string[];
  }> {
    return await page.evaluate((sel) => {
      const element = document.querySelector(sel) as HTMLElement;
      if (!element) {
        return { isObscured: true, obscuringElements: ['Element not found'] };
      }

      const rect = element.getBoundingClientRect();
      const obscuringElements: string[] = [];

      // Check center point of element
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      const elementAtCenter = document.elementFromPoint(centerX, centerY);

      if (elementAtCenter && !element.contains(elementAtCenter)) {
        const obscuringElement = elementAtCenter as HTMLElement;
        const style = window.getComputedStyle(obscuringElement);

        // Check if it's a fixed or sticky element that could obscure
        if (style.position === 'fixed' || style.position === 'sticky') {
          obscuringElements.push(obscuringElement.tagName.toLowerCase());
        }
      }

      return {
        isObscured: obscuringElements.length > 0,
        obscuringElements
      };
    }, selector);
  }

  /**
   * Check for proper spacing between interactive elements
   */
  static async checkElementSpacing(page: Page): Promise<string[]> {
    const spacingIssues: string[] = [];
    
    try {
      const interactiveElements = await page.evaluate(() => {
        const elements: Array<{selector: string; rect: DOMRect}> = [];
        const selectors = ['button', 'a[href]', 'input', '[role="button"]'];
        
        selectors.forEach(selector => {
          const nodeList = document.querySelectorAll(selector);
          nodeList.forEach((element, index) => {
            const rect = element.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              elements.push({
                selector: `${selector}:nth-of-type(${index + 1})`,
                rect
              });
            }
          });
        });
        
        return elements;
      });
      
      // Check spacing between adjacent interactive elements
      for (let i = 0; i < interactiveElements.length; i++) {
        for (let j = i + 1; j < interactiveElements.length; j++) {
          const el1 = interactiveElements[i];
          const el2 = interactiveElements[j];
          
          const distance = Math.sqrt(
            Math.pow(el1.rect.x - el2.rect.x, 2) + 
            Math.pow(el1.rect.y - el2.rect.y, 2)
          );
          
          // WCAG recommends at least 8px spacing between targets
          if (distance < 8 && distance > 0) {
            spacingIssues.push(`Insufficient spacing between ${el1.selector} and ${el2.selector}`);
          }
        }
      }
    } catch (error) {
      console.error('Error checking element spacing:', error);
      spacingIssues.push('Error during spacing analysis');
    }
    
    return spacingIssues;
  }
}
